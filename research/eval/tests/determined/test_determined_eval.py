"""Test the determined eval runs."""

import glob
import json
from pathlib import Path

import numpy as np
import pytest

from research.core.constants import AUGMENT_EFS_ROOT
from research.eval.eval_lib import wait_experiment
from research.eval.tests.determined.determined_common import (
    get_trials,
    gpt_neox_dir,
    launch_eval,
    main_only,
    test_config_dir,
)


def _validate_eval_results(results_prefix, baseline_path):
    # Retrieve the latest evaluation report
    report = f"{results_prefix}*_eval_results_*.json"
    print(f"Checking {report}")
    files = glob.glob(report)
    assert len(files) == 1, f"Expected only one evaluation report, found {files}"
    results_path = Path(files[0])
    rec = json.loads(results_path.read_text(encoding="utf-8"))

    # Retrieve the expected results
    baseline = json.loads(baseline_path.read_text(encoding="utf-8"))

    for task in rec["results"]:
        # The results should compare exactly, since we run on standard hardware
        test_results = rec["results"][task]["bits_per_byte"]
        baseline_results = baseline["results"][task]["bits_per_byte"]
        if not np.allclose(test_results, baseline_results, rtol=0.001):
            baseline_rel = baseline_path.relative_to(gpt_neox_dir)
            msg = (
                f"\nEvaluation results do not match within the tolerance (test vs baseline):\n"
                f"{test_results} vs {baseline_results}\n"
                f"For a detailed analysis, compare: {results_path}\n"
                f"with the baseline at: {baseline_rel}.\n"
                f"If the test result represents the new baseline, please \n"
                f"cp {results_path} {baseline_rel}"
            )
            raise AssertionError(msg)


@main_only
@pytest.mark.parametrize(
    "metaconfig,baseline_path",
    [
        pytest.param(
            test_config_dir / "hydra-short.yaml",
            Path(__file__).parent / "hydra_baseline_short.json",
            id="hydra_short",
            marks=[
                pytest.mark.parallel,
            ],
        ),
    ],
)
def test_hydra(metaconfig, baseline_path):
    """Test hydra jobs against a baseline."""
    experiment_id: int = launch_eval(metaconfig.as_posix(), cluster="GCP-US1")
    wait_experiment(experiment_id)
    trials = get_trials(experiment_id)

    results_out = str(
        AUGMENT_EFS_ROOT
        / f"eval/jobs/GH/gh-{experiment_id}-{trials[0].id}/*_hydra.jsonl"
    )
    print(f"Checking {results_out}")
    files: list[str] = glob.glob(results_out)
    assert len(files) == 1, f"Expected only one report out, found {files}"

    results_path = Path(files[0])
    scores = {}
    with results_path.open(encoding="utf-8") as f:
        json_recs = [json.loads(x) for x in f]

    scores = {x["patch_id"]: x["_extra"]["result"] for x in json_recs}

    # Retrieve the expected results
    baseline = json.loads(baseline_path.read_text(encoding="utf-8"))
    assert (
        set(scores.keys()) == set(baseline.keys())
    ), f"Key mismatches between scores and baseline: {set(scores.keys())^set(baseline.keys())}"

    # Check that the scores are the same as the baseline
    discrepancies = [
        (key, f"{scores[key]} != {baseline[key]}")
        for key in scores
        if scores[key] != baseline[key]
    ]
    assert (
        len(discrepancies) == 0
    ), f"Found the following discrepancies: {discrepancies}"

    passes = [1 for v in scores.values() if v == "PASSED"]
    assert sum(passes) > 0
