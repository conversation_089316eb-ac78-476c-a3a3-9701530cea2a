package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialVerisoulDryRunFlag = featureflags.NewBoolFlag("free_trial_verisoul_dry_run", true)

type FreeTrialVerisoulJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure DisposableEmailDomainJob implements the Job interface
var _ Job = (*FreeTrialVerisoulJob)(nil)

func NewFreeTrialVerisoulJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialVerisoulJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialVerisoulJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-verisoul",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialVerisoulJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialVerisoulJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting disposable email suspect users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error suspending users: %w", err)
	}

	return nil
}

type verisoulSuspect struct {
	ID               string    `bigquery:"opaque_user_id"`
	TenantID         string    `bigquery:"tenant_id"`
	Email            string    `bigquery:"email"`
	Tier             string    `bigquery:"tier"`
	CreatedAt        time.Time `bigquery:"created_at"`
	AccountScore     float32   `bigquery:"account_score"`
	MultipleAccounts float32   `bigquery:"multiple_accounts"`
	AccountsLinked   int       `bigquery:"accounts_linked"`
	Bot              float32   `bigquery:"bot"`
	RiskSignals      float32   `bigquery:"risk_signals"`
	Reason           string    `bigquery:"reason"`
}

func (m *FreeTrialVerisoulJob) getSuspects(ctx context.Context) ([]*verisoulSuspect, error) {
	// Construct the query.
	// Using verisoul data to identify disposable email users.
	query := m.bqClient.Query(`
	WITH
		verisoul_report AS (
			SELECT
				time,
				LOWER(JSON_EXTRACT_SCALAR(report, '$.account.email.email')) AS email,
				CAST(JSON_EXTRACT_SCALAR(report, '$.account_score') AS FLOAT64) AS account_score,
				CAST(JSON_EXTRACT_SCALAR(report, '$.multiple_accounts') AS FLOAT64) AS multiple_accounts,
				CAST(JSON_EXTRACT_SCALAR(report, '$.accounts_linked') AS INT64) AS accounts_linked,
				CAST(JSON_EXTRACT_SCALAR(report, '$.bot') AS FLOAT64) AS bot,
				CAST(JSON_EXTRACT_SCALAR(report, '$.risk_signals') AS FLOAT64) AS risk_signals
			FROM verisoul
			WHERE report IS NOT NULL
		),
		verisoul_report_ranked AS (
			SELECT
				verisoul_report.*,
				ROW_NUMBER() OVER (PARTITION BY email ORDER BY time DESC) as rn
			FROM verisoul_report
	  	),
		verisoul_score AS (
			SELECT
				*
			FROM verisoul_report_ranked
			WHERE rn = 1
		),

		recent_user_changes AS (
			SELECT
				augment_user_id AS opaque_user_id,
				tenant_id,
				LOWER(user_email) AS email,
				time AS created_at,
				ROW_NUMBER() OVER (PARTITION BY augment_user_id ORDER BY time DESC) as rn
			FROM add_user_to_tenant
			WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
		),
		recent_user AS (
			SELECT
			*
			FROM recent_user_changes
			WHERE rn = 1
		),

		-- determine service tier
		tier AS (
			SELECT
				id as tenant_id,
				CASE
					WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
						THEN 'TEAM'
					ELSE tier
				END AS tier
			FROM tenant
		),
		-- identify suspended users where possible
		user_id AS (
		SELECT
			id as opaque_user_id,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE'
			) as trial_suspended,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_COMMUNITY_ABUSE'
			) as community_suspended,
			suspension_exempt
		FROM user
		),
		profile AS (
			SELECT
				ru.opaque_user_id,
				ru.tenant_id,
				ru.email,
				tier.tier,
				user_id.trial_suspended,
				user_id.community_suspended,
				user_id.suspension_exempt,
				ru.created_at,
				vs.account_score,
				vs.multiple_accounts,
				vs.accounts_linked,
				vs.bot,
				vs.risk_signals,
			FROM recent_user ru
			JOIN tier ON ru.tenant_id = tier.tenant_id
			JOIN verisoul_score vs ON vs.email = ru.email
			LEFT JOIN user_id ON ru.opaque_user_id = user_id.opaque_user_id
		),
		suspects AS(
			SELECT DISTINCT
				profile.*,
				CASE
					WHEN bot = 1 THEN "BOT"
					WHEN multiple_accounts = 1 AND accounts_linked >= 5 THEN "MULTIPLE_ACCOUNTS"
					WHEN account_score = 1 THEN "ACCOUNT_RISK"
					WHEN risk_signals = 1 THEN "RISK_SIGNALS"
				END as reason
			FROM profile
			WHERE tier IN ('PROFESSIONAL', 'TEAM', 'COMMUNITY')
			-- discard those that are already suspended or exempt
			AND NOT (profile.community_suspended AND tier = 'COMMUNITY')
			AND NOT (profile.trial_suspended and tier IN ('PROFESSIONAL', 'TEAM'))
			AND NOT profile.suspension_exempt
		)

		SELECT
			opaque_user_id,
			tenant_id,
			email,
			tier,
			created_at,
			account_score,
			multiple_accounts,
			accounts_linked,
			bot,
			risk_signals,
			reason
		FROM suspects
		WHERE reason IS NOT NULL
		ORDER BY created_at DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var suspects []*verisoulSuspect
	for {
		var row verisoulSuspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			suspects = append(suspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(suspects))
	return suspects, nil
}

func (m *FreeTrialVerisoulJob) suspendSuspects(
	ctx context.Context,
	suspects []*verisoulSuspect,
) error {
	dryRun, err := freeTrialVerisoulDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	communitySuspensionsEnabled := communitySuspensionsEnabled(m.featureFlagHandle)

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()

	// Get wildcard token for suspension operations (no tenant-specific token needed)
	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
		return fmt.Errorf("error getting wildcard token: %w", err)
	}
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

	for _, suspect := range suspects {

		// Check if the user is exempt or already suspended for using disposable email
		userObj, err := m.authClient.GetUser(ctx, requestCtx, suspect.ID, &suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", suspect.ID, suspect.TenantID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt in tenant %s", suspect.ID, suspect.TenantID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse in tenant %s", suspect.ID, suspect.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		// Community accounts are not suspended if the feature flag is not enabled.
		if suspect.Tier == "COMMUNITY" && !communitySuspensionsEnabled {
			MisuseActionOutcome.WithLabelValues(m.jobName, "community_account_suspension_disabled", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		// not 100% certain of some indicators. Filter them out here, but count incidents.
		if suspect.Reason == "RISK_SIGNALS" {
			MisuseActionOutcome.WithLabelValues(m.jobName, "risk_signals", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if suspect.Reason == "ACCOUNT_RISK" {
			MisuseActionOutcome.WithLabelValues(m.jobName, "account_risk", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		evidence := fmt.Sprintf("Verisoul report indicates suspicious account, reason: %s, details: account_score: %f, multiple_accounts: %f, accounts_linked: %d, bot: %f, risk_signals: %f",
			suspect.Reason, suspect.AccountScore, suspect.MultipleAccounts, suspect.AccountsLinked, suspect.Bot, suspect.RiskSignals)
		log.Info().Msgf("Misuse monitor detected suspicious account by user %s in tenant %s. %s",
			suspect.ID, suspect.TenantID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionType := auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE
			if suspect.Tier == "COMMUNITY" {
				suspensionType = auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE
			}
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, suspect.ID, suspect.TenantID, suspensionType, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", suspect.ID, suspect.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, suspect.ID, suspect.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
