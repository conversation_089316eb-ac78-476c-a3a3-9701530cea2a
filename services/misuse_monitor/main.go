package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

type JobConfig struct {
	JobName           string
	ExecutionInterval string
}

type Config struct {
	PromPort                    int
	Port                        int
	ClientMtls                  *tlsconfig.ClientConfig
	ServerMtls                  *tlsconfig.ServerConfig
	Namespace                   string
	ProjectId                   string
	DatasetName                 string
	AuthEndpoint                string
	TokenExchangeEndpoint       string
	TenantWatcherEndpoint       string
	FeatureFlagsSdkKeyPath      string
	DynamicFeatureFlagsEndpoint string
	GCSBucketName               string
	Jobs                        []JobConfig
}

// Load configuration from the given file.
func loadConfig(configFile string) (*Config, error) {
	var config Config
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, err
	}

	log.Info().Msgf("Config: %v", config)
	return &config, nil
}

func main() {
	logging.SetupServerLogging()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // Ensure context is cancelled when main exits

	// Create a wait group to track goroutines
	var wg sync.WaitGroup

	// Parse flags.
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config.
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error loading config")
	}

	// Start metrics server.
	wg.Add(1)
	go func() {
		defer wg.Done()
		http.Handle("/metrics", promhttp.Handler())
		server := &http.Server{Addr: fmt.Sprintf(":%d", config.PromPort)}

		// Shutdown the server when context is cancelled
		go func() {
			<-ctx.Done()
			log.Info().Msg("Shutting down metrics server")
			if err := server.Shutdown(context.Background()); err != nil {
				log.Error().Err(err).Msg("Error shutting down metrics server")
			}
		}()

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Set up feature flags.
	featureFlagHandle, err := featureflags.NewFeatureFlagHandleFromFile(
		config.FeatureFlagsSdkKeyPath, config.DynamicFeatureFlagsEndpoint,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	// Set up clients needed for jobs.
	authClient, err := authclient.New(config.AuthEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth client")
	}
	defer authClient.Close()
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
	}
	defer tokenExchangeClient.Close()
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(clientCreds))
	// Use a longer expiration to try to ensure that at least one set of job
	// runs will share cache.
	tenantCache := tenantwatcherclient.NewTenantCacheSync(tenantWatcherClient, tenantwatcherclient.WithCacheTTL(15*time.Minute))
	defer tenantCache.Close()

	// Create gRPC server
	grpcServer := NewMisuseMonitorServer()

	// Map to store jobs for cleanup
	jobs := make(map[string]Job)

	// Create and start job runners based on config
	for _, jobConfig := range config.Jobs {
		var job Job
		var err error

		// Create the appropriate job based on the job name
		switch jobConfig.JobName {
		case "api-misuse":
			job, err = NewMisuseMonitorJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, tenantCache,
				featureFlagHandle, config.GCSBucketName,
			)
		case "free-trial-duplicate":
			job, err = NewFreeTrialDuplicationJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "free-trial-recent-user-duplicate":
			job, err = NewFreeTrialRecentUserDuplicationJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "free-trial-feature-vector-duplicate-v2":
			job, err = NewFreeTrialFeatureVectorDuplicationV2Job(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "free-trial-fv-session-duplicate":
			job, err = NewFreeTrialFVSessionDuplicationJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "free-trial-verisoul":
			job, err = NewFreeTrialVerisoulJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "free-trial-conversation-duplicate":
			job, err = NewFreeTrialConversationDuplicationJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "disposable-email-domain":
			job, err = NewDisposableEmailDomainJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		case "free-trial-forgiveness":
			job, err = NewFreeTrialForgivenessJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, tenantCache,
				featureFlagHandle,
			)
		case "missing-feature-vectors":
			job, err = NewMissingFeatureVectorsJob(
				ctx, config.ProjectId, config.DatasetName, authClient, tokenExchangeClient, featureFlagHandle,
			)
		default:
			log.Error().Str("job_name", jobConfig.JobName).Msg("Unknown job type, skipping")
			continue
		}

		if err != nil {
			log.Error().Err(err).Str("job_name", jobConfig.JobName).Msg("Error creating job, skipping")
			continue
		}

		// Store the job for cleanup
		jobs[jobConfig.JobName] = job

		// Parse the execution interval
		interval, err := time.ParseDuration(jobConfig.ExecutionInterval)
		if err != nil {
			log.Error().
				Err(err).
				Str("job_name", jobConfig.JobName).
				Str("interval", jobConfig.ExecutionInterval).
				Msg("Invalid execution interval, using default of 15m")
			interval = 15 * time.Minute
		}

		// Create and start the job runner
		runner := NewJobRunner(jobConfig.JobName, job, interval)
		runner.Start(ctx, &wg)

		// Register the job runner with the gRPC server
		grpcServer.RegisterJobRunner(jobConfig.JobName, runner)

		log.Info().
			Str("job_name", jobConfig.JobName).
			Str("interval", jobConfig.ExecutionInterval).
			Msg("Started job runner")
	}

	// Start the gRPC server
	if err := StartGRPCServer(ctx, config, grpcServer, &wg); err != nil {
		log.Fatal().Err(err).Msg("Failed to start gRPC server")
	}

	// Set up a signal handler for graceful shutdown
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Wait for termination signal
	<-sigCh
	log.Info().Msg("Received termination signal, shutting down")

	cancel()
	wg.Wait()

	// Close all jobs
	for name, job := range jobs {
		log.Info().Str("job_name", name).Msg("Closing job")
		job.Close()
	}

	log.Info().Msg("Shutdown complete")
}
