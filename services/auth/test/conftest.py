"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import logging
import os
import requests
import tempfile
from typing import Generator
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

import pytest
from google.cloud import pubsub_v1

from base.test_utils import bigtable_emulator as bigtable_emulator_process_runner
from base.test_utils import bigtable_setup
from base.test_utils.pubsub import pubsub_emulator as pubsub_emulator_process_runner
from services.auth.central.client import auth_client
from services.auth.central.server import (
    auth_central_test_setup,
    auth_pb2_grpc,
    bigtable_connector,
)
from services.auth.central.server import auth_entities_pb2
from services.auth.central.server.config import (
    Auth0Config,
    AuthConfig,
    TenantWatcherConfig,
)
from services.auth.query.server import auth_query_test_setup
from services.lib.request_context.request_context import Request<PERSON>ontext
from services.tenant_watcher import tenant_watcher_pb2
from services.tenant_watcher.server import tenant_watcher_test_setup
from services.token_exchange import token_scopes_pb2
from services.token_exchange.server import token_exchange_test_setup

VALID_CLIENT_ID = "augment-vscode-extension"
VALID_REDIRECT_URI = "vscode://augment.vscode-augment/auth/result"
VALID_CODE_CHALLENGE = auth_central_test_setup.VALID_CODE_CHALLENGE

VALID_LOCALHOST_CLIENT_ID = "augment-intellij-plugin"
VALID_LOCALHOST_REDIRECT_URI = "http://127.0.0.1:1234/api/augment/auth/result"
VALID_LOCALHOST_REDIRECT_URI_NO_PORT = "http://127.0.0.1/api/augment/auth/result"

VALID_VIM_CLIENT_ID = "augment-vim-extension"
VALID_VIM_SHORT_CLIENT_ID = "v"
VALID_VIM_REDIRECT_URI = ""

CUSTOMER_UI_CLIENT_ID = "customer-ui"

CENTRAL_NAMESPACE = "central-dev"

VALID_ENTERPRISE_NAMESPACE = "augment"
VALID_ENTERPRISE_TENANT_NAME = "augment"
VALID_TENANT_REDIRECT_URL = "https://bogus.url/"
VALID_ENTERPRISE_TENANT_ID = "test123"
SAMPLE_TENANT_ID = "sample123"

VALID_PROFESSIONAL_NAMESPACE = "d0"
VALID_SELF_SERVE_TENANT_NAME = "self-serve-team"
VALID_SELF_SERVE_TENANT_ID = "test456"

VALID_PROFESSIONAL_TENANT_NAME = "professional"
VALID_PROFESSIONAL_TENANT_ID = "test789"

VALID_COMMUNITY_NAMESPACE = "i0"
VALID_COMMUNITY_TENANT_NAME = "community"
VALID_COMMUNITY_TENANT_ID = "test012"

ASYNC_OPS_TOPIC = "test-async-ops-topic"
ASYNC_OPS_SUBSCRIPTION = "test-async-ops-sub"
PROJECT_ID = "test-project"


@pytest.fixture(scope="module")
def bigtable_emulator():
    """Fixture to start a Bigtable emulator."""
    yield from bigtable_emulator_process_runner.start_emulator()


@pytest.fixture(scope="module")
def auth_central_bigtable(bigtable_emulator):
    """Fixture to create a Bigtable table."""
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator

    bigtable_connector.setup_table(
        instance_id="test-instance", table_name="test-table", project_id=PROJECT_ID
    )
    yield bigtable_setup.BigtableTable(
        instance="test-instance", table_name="test-table", project=PROJECT_ID
    )


@pytest.fixture(scope="module")
def tenants(auth_central_bigtable) -> list[tenant_watcher_pb2.Tenant]:
    """The default test tenant with subscription setup."""
    # Set up test data directly in BigTable for the tenant fixture
    # Connect to BigTable
    table = bigtable_connector.connect(
        instance_id=auth_central_bigtable.instance,
        table_name=auth_central_bigtable.table_name,
        project_id=auth_central_bigtable.project,
    )

    # Define common IDs used across all entities
    subscription_id = "sub_test456"
    stripe_customer_id = "cus_test456"

    # 1. Create Subscription
    subscription_row_key = f"Subscription#{subscription_id}"

    # Build Subscription proto
    subscription = auth_entities_pb2.Subscription(
        subscription_id=subscription_id,
        stripe_customer_id=stripe_customer_id,
        price_id="price_test",
        status=auth_entities_pb2.Subscription.Status.ACTIVE,
        seats=5,  # 5 seats for the tenant
        tenant_id=VALID_SELF_SERVE_TENANT_ID,
        cancel_at_period_end=False,
        has_payment_method=True,
    )

    # Serialize and write to BigTable
    subscription_data = subscription.SerializeToString()
    subscription_row = table.direct_row(subscription_row_key)
    subscription_row.set_cell("Subscription", "value", subscription_data)
    subscription_row.commit()

    # 2. Create TenantSubscriptionMapping
    mapping_row_key = f"TenantSubscriptionMapping#{VALID_SELF_SERVE_TENANT_ID}"

    # Build TenantSubscriptionMapping proto
    mapping = auth_entities_pb2.TenantSubscriptionMapping(
        tenant_id=VALID_SELF_SERVE_TENANT_ID,
        orb_subscription_id=subscription_id,
        stripe_customer_id=stripe_customer_id,
    )

    # Serialize and write to BigTable
    mapping_data = mapping.SerializeToString()
    mapping_row = table.direct_row(mapping_row_key)
    mapping_row.set_cell("TenantSubscriptionMapping", "value", mapping_data)
    mapping_row.commit()

    # Return the tenant list
    return [
        tenant_watcher_pb2.Tenant(
            id=VALID_ENTERPRISE_TENANT_ID,
            name=VALID_ENTERPRISE_TENANT_NAME,
            shard_namespace=VALID_ENTERPRISE_NAMESPACE,
            cloud="CLOUD_DEV",
            auth_configuration=tenant_watcher_pb2.AuthConfiguration(
                domain="augmentcode.com",
                username_domains=["augmentcorp.com"],
                email_address_domains=["augmentcomputing.com"],
                allowed_identity_providers=["idp"],
            ),
            tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
        ),
        # This tenant has the auth configuration similar to most of our existing
        # tenants as of 2024-09-25.
        tenant_watcher_pb2.Tenant(
            id=SAMPLE_TENANT_ID,
            name=SAMPLE_TENANT_ID,
            shard_namespace=VALID_ENTERPRISE_NAMESPACE,
            cloud="CLOUD_DEV",
            auth_configuration=tenant_watcher_pb2.AuthConfiguration(
                domain="sample.com",
            ),
            tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
        ),
        tenant_watcher_pb2.Tenant(
            id=VALID_SELF_SERVE_TENANT_ID,
            name=VALID_SELF_SERVE_TENANT_NAME,
            shard_namespace=VALID_PROFESSIONAL_NAMESPACE,
            cloud="CLOUD_DEV",
            auth_configuration=tenant_watcher_pb2.AuthConfiguration(
                domain="self-serve-team.com",
            ),
            tier=tenant_watcher_pb2.TenantTier.PROFESSIONAL,
            config=tenant_watcher_pb2.Config(
                configs={"is_self_serve_team": "true"},
            ),
        ),
        tenant_watcher_pb2.Tenant(
            id=VALID_PROFESSIONAL_TENANT_ID,
            name=VALID_PROFESSIONAL_TENANT_NAME,
            shard_namespace=VALID_PROFESSIONAL_NAMESPACE,
            cloud="CLOUD_DEV",
            auth_configuration=tenant_watcher_pb2.AuthConfiguration(
                domain="self-serve-team.com",
            ),
            tier=tenant_watcher_pb2.TenantTier.PROFESSIONAL,
            config=tenant_watcher_pb2.Config(
                configs={"is_self_serve_team": "false"},
            ),
        ),
        tenant_watcher_pb2.Tenant(
            id=VALID_COMMUNITY_TENANT_ID,
            name=VALID_COMMUNITY_TENANT_NAME,
            shard_namespace=VALID_COMMUNITY_NAMESPACE,
            cloud="CLOUD_DEV",
            auth_configuration=tenant_watcher_pb2.AuthConfiguration(
                domain="community-team.com",
            ),
            tier=tenant_watcher_pb2.TenantTier.COMMUNITY,
        ),
    ]


@pytest.fixture(scope="module")
def tenant_watcher_fake_server_port(tenants):
    """Fixture to start the tenant watcher server."""
    yield from tenant_watcher_test_setup.start_fake_tenant_watcher_server(
        tenants=tenants,
    )


@pytest.fixture(scope="module")
def token_exchange_server(tenant_watcher_fake_server_port):
    """Fixture to start the token exchange server."""
    config = token_exchange_test_setup.TokenExchangeConfig(
        port=0,  # Find some unused port
        tenant_watcher_endpoint=f"localhost:{tenant_watcher_fake_server_port}",
        service_token_configs=[
            token_exchange_test_setup.TokenForServiceConfig(
                regex=".*",
                scopes=["AUTH_RW"],
                expiration="60m",
            )
        ],
    )
    yield from token_exchange_test_setup.start_token_exchange_server(config)


@pytest.fixture
def request_context(token_exchange_server) -> RequestContext:
    """Fixture to create a request context."""
    return token_exchange_server.create_test_request_context(
        tenant_id=VALID_ENTERPRISE_TENANT_ID,
        namespace=VALID_ENTERPRISE_NAMESPACE,
        scopes=[token_scopes_pb2.AUTH_RW],
    )


@pytest.fixture
def request_context_professional(token_exchange_server) -> RequestContext:
    """Fixture to create a request context."""
    return token_exchange_server.create_test_request_context(
        tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        namespace=VALID_PROFESSIONAL_NAMESPACE,
        scopes=[token_scopes_pb2.AUTH_RW],
    )


@pytest.fixture
def request_context_self_serve(token_exchange_server) -> RequestContext:
    """Fixture to create a request context."""
    return token_exchange_server.create_test_request_context(
        tenant_id=VALID_SELF_SERVE_TENANT_ID,
        namespace=VALID_PROFESSIONAL_NAMESPACE,
        scopes=[token_scopes_pb2.AUTH_RW],
    )


@pytest.fixture
def request_context_community(token_exchange_server) -> RequestContext:
    """Fixture to create a request context."""
    return token_exchange_server.create_test_request_context(
        tenant_id=VALID_COMMUNITY_TENANT_ID,
        namespace=VALID_COMMUNITY_NAMESPACE,
        scopes=[token_scopes_pb2.AUTH_RW],
    )


@pytest.fixture
def admin_request_context(token_exchange_server) -> RequestContext:
    """Fixture to create a request context with admin permissions for SuspensionCleanup."""
    # Create a request context with empty tenant ID and AUTH_RW scope
    # SuspensionCleanup requires empty tenant ID and AUTH_RW scope
    return token_exchange_server.create_test_request_context(
        tenant_id="",  # Empty tenant ID for admin operations
        namespace=VALID_ENTERPRISE_NAMESPACE,
        scopes=[token_scopes_pb2.Scope.AUTH_RW],
    )


@pytest.fixture(scope="module")
def auth_query_server(
    auth_central_grpc_server,
    tenant_watcher_fake_server_port,
    token_exchange_server,
):
    """Fixture to start the auth query server."""
    yield from auth_query_test_setup.start_auth_query_server(
        config=auth_query_test_setup.AuthQueryConfig.create(
            auth_central_endpoint="localhost:%s" % auth_central_grpc_server.grpc_port,
            tenant_watcher_endpoint=("localhost:%s" % tenant_watcher_fake_server_port),
            token_exchange_endpoint="localhost:%s" % token_exchange_server.port,
            namespace=VALID_ENTERPRISE_NAMESPACE,
        ),
    )


@pytest.fixture(scope="module")
def auth_secrets_file(tmp_path_factory):
    """Fixture to create a temporary secrets file."""
    tmp_file = tmp_path_factory.mktemp("auth-central") / "auth.json"
    tmp_file.write_text("test-secret")
    return tmp_file


@pytest.fixture(scope="module")
def auth0_secrets_file(tmp_path_factory):
    """Fixture to create a temporary auth0 secrets file."""
    tmp_file = tmp_path_factory.mktemp("auth-central") / "auth0.txt"
    tmp_file.write_text("test-client-id\ntest-client-secret")
    return tmp_file


class EchoHandler(BaseHTTPRequestHandler):
    """HTTP handler that echoes the method and path."""

    def do_GET(self):
        self._handle_request()

    def _handle_request(self):
        """Handle any HTTP request by echoing method and path."""
        response_data = {
            "method": self.command,
            "path": self.path,
            "headers": dict(self.headers),
        }

        response_json = json.dumps(response_data, indent=2)

        self.send_response(200)
        self.send_header("Content-Type", "application/json")
        self.send_header("Content-Length", str(len(response_json)))
        self.end_headers()
        self.wfile.write(response_json.encode("utf-8"))

    def log_message(self, format, *args):
        """Suppress default logging."""
        pass


@pytest.fixture(scope="module")
def dummy_echo_web_server():
    """Fixture to start a dummy web server that echoes method and path."""
    # Create server with port 0 to get an available port
    server = HTTPServer(("localhost", 0), EchoHandler)
    server_thread = threading.Thread(target=server.serve_forever, daemon=True)

    # Start the server in a background thread
    server_thread.start()

    # Get the actual port assigned
    host, port = server.server_address
    base_url = f"http://{host}:{port}"

    while True:
        try:
            requests.get(base_url, timeout=10)
            break
        except requests.exceptions.ConnectionError:
            time.sleep(0.1)
            continue

    try:
        yield base_url
    finally:
        server.shutdown()
        server.server_close()
        server_thread.join(timeout=1)


@pytest.fixture(scope="module")
def pubsub_emulator():
    """Fixture to start a PubSub emulator."""
    yield from pubsub_emulator_process_runner.start_emulator()


@pytest.fixture(scope="module")
def async_ops_pubsub_topic(pubsub_emulator):
    """Fixture to create a PubSub topic for async ops."""
    os.environ["PUBSUB_EMULATOR_HOST"] = pubsub_emulator
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(PROJECT_ID, ASYNC_OPS_TOPIC)
    publisher.create_topic(request={"name": topic_path})
    logging.info("Created topic %s", topic_path)
    return topic_path


@pytest.fixture(scope="module")
def async_ops_pubsub_subscription(pubsub_emulator, async_ops_pubsub_topic):
    """Fixture to create a PubSub subscription for async ops."""
    os.environ["PUBSUB_EMULATOR_HOST"] = pubsub_emulator
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(PROJECT_ID, ASYNC_OPS_SUBSCRIPTION)
    subscriber.create_subscription(
        request={"name": subscription_path, "topic": async_ops_pubsub_topic}
    )
    logging.info("Created subscription %s", subscription_path)
    return subscription_path


@pytest.fixture(scope="module")
def client_config_map(dummy_echo_web_server):
    return {
        VALID_CLIENT_ID: auth_central_test_setup.OAuthClientConfig(
            name="Visual Studio Code",
            redirect_uris=[VALID_REDIRECT_URI],
        ),
        VALID_LOCALHOST_CLIENT_ID: auth_central_test_setup.OAuthClientConfig(
            name="Intellij",
            redirect_uris=[VALID_LOCALHOST_REDIRECT_URI_NO_PORT],
        ),
        VALID_VIM_CLIENT_ID: auth_central_test_setup.OAuthClientConfig(
            name="Vim",
            redirect_uris=[VALID_VIM_REDIRECT_URI],
        ),
        VALID_VIM_SHORT_CLIENT_ID: auth_central_test_setup.OAuthClientConfig(
            name="Vim",
            redirect_uris=[VALID_VIM_REDIRECT_URI],
        ),
        CUSTOMER_UI_CLIENT_ID: auth_central_test_setup.OAuthClientConfig(
            name="Customer UI",
            redirect_uris=[dummy_echo_web_server + "/customer-ui"],
            instant_redirect=True,
            reuse_session_cookie=True,
        ),
    }


@pytest.fixture(scope="module")
def auth_central_config(
    auth_central_bigtable,
    tenant_watcher_fake_server_port,
    token_exchange_server,
    request,
    auth_secrets_file,
    auth_central_grpc_server,
    fake_oidc_server,
    auth0_secrets_file,
    client_config_map,
):
    return auth_central_test_setup.AuthCentralConfig(
        auth_config=AuthConfig(
            token_exchange_endpoint=f"localhost:{token_exchange_server.port}",
        ),
        backend_port=auth_central_grpc_server.private_port,
        bigtable_table=auth_central_bigtable,
        client_config_map=client_config_map,
        tenant_watcher=TenantWatcherConfig(
            tenant_watcher_endpoint=("localhost:%s" % tenant_watcher_fake_server_port),
            api_proxy_hostname_domain="t.augmentcode.com",
        ),
        secrets_path=str(auth_secrets_file),
        login_auth0=Auth0Config(
            credentials_path=str(auth0_secrets_file),
            server_metadata_url=fake_oidc_server.well_known_config_url(),
        ),
    )


@pytest.fixture(scope="module")
def auth_central_grpc_config(
    tenant_watcher_fake_server_port,
    token_exchange_server,
    auth_central_bigtable,
    async_ops_pubsub_topic,
    async_ops_pubsub_subscription,
) -> Generator[auth_central_test_setup.AuthCentralGrpcConfig, None, None]:
    with tempfile.NamedTemporaryFile(mode="w+t") as disposable_domains_file:
        disposable_domains_file.write("disposable.com\nflushable.com")
        disposable_domains_file.flush()

        yield auth_central_test_setup.AuthCentralGrpcConfig(
            auth_config=AuthConfig(
                token_exchange_endpoint=f"localhost:{token_exchange_server.port}",
            ),
            bigtable_table=auth_central_bigtable,
            grpc_server=auth_central_test_setup.GrpcGoConfig(ports=[0]),
            tenant_watcher=TenantWatcherConfig(
                tenant_watcher_endpoint=(
                    "localhost:%s" % tenant_watcher_fake_server_port
                ),
                api_proxy_hostname_domain="t.augmentcode.com",
            ),
            prometheus_bind_address="127.0.0.1:0",
            async_ops=auth_central_test_setup.AsyncOpsConfig(
                topic_name=ASYNC_OPS_TOPIC,
                subscription_name=ASYNC_OPS_SUBSCRIPTION,
            ),
            orb=auth_central_test_setup.OrbConfig(
                enabled=True,
                plans=[
                    auth_central_test_setup.PlanConfig(
                        id="orb_trial_plan",
                        features=auth_central_test_setup.PlanFeatures(
                            plan_type="trial"
                        ),
                    ),
                    auth_central_test_setup.PlanConfig(
                        id="orb_developer_plan",
                        features=auth_central_test_setup.PlanFeatures(plan_type="paid"),
                    ),
                ],
            ),
            disposable_email_domains_path=disposable_domains_file.name,
        )


@pytest.fixture(scope="module")
def auth_central_grpc_server(
    auth_central_grpc_config,
) -> Generator[auth_central_test_setup.AuthCentralGrpcServer, None, None]:
    """Fixture to start the auth grpc server."""
    yield from auth_central_test_setup.start_auth_central_grpc_server(
        namespace=CENTRAL_NAMESPACE, config=auth_central_grpc_config
    )


@pytest.fixture(scope="module")
def fake_oidc_server():
    """Start an OIDC server for testing."""
    yield from auth_central_test_setup.start_fake_oidc_server()


@pytest.fixture(scope="module")
def auth_central_http_server_raw(
    auth_central_config,
    fake_oidc_server: auth_central_test_setup.FakeOidcServer,
) -> Generator[auth_central_test_setup.AuthCentralHttpServer, None, None]:
    """Fixture to start the auth server."""
    yield from auth_central_test_setup.start_auth_central_http_server(
        CENTRAL_NAMESPACE, auth_central_config, fake_oidc_server
    )


@pytest.fixture(scope="module")
def auth_central_grpc_client(
    auth_central_grpc_server: auth_central_test_setup.AuthCentralGrpcServer,
):
    """Fixture to start the auth grpc client."""
    client: auth_pb2_grpc.AuthServiceStub = auth_client.setup_stub(
        endpoint="localhost:%s" % auth_central_grpc_server.grpc_port,
        credentials=None,
    )
    return client
