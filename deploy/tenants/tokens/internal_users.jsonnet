// please do not add to this list unless you have a strict reason where oauth is not working.
function(tenant_name)
  [
    {
      user_id: 'csapuntz',
      token_sha256: '4725ab5bf15e12e80a6799c444162d75065e2220de81db49022ff3b87b262b81',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'evan',
      token_sha256: 'eb5edf42c1544f068b3620759beda8927a2b0883bf904e9e3f6a697891aae0ff',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'rich',
      token_sha256: 'f2d55e12484e9813837755ba7aec5d4a86f1b2c6df54a0bc68706c6802ae48a3',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'xuanyi',
      token_sha256: '3e202b916c7c4c605f1bfb883e5b8ccf80bc6f1b79954fc8dbe31a1e3d05e6d0',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'marcmac',
      token_sha256: '5e22cbf2f889d8a03caed7926f4a2f8d3b6d185632131948fb4c052b844b00f9',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'fedor-vscode',
      token_sha256: 'a30feee5cb209f6873a41a97dcc12cb4af050e864c47052bdf7a56fcdf855f4e',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'fedor-intellij',
      token_sha256: 'c2834be1ee3c1bb97bdd420659118889bc823bdd973fd0230aa76efd732c40d7',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'liyangguang',
      token_sha256: '709ff427bdc54d0e364ef0dade9e07b1b2aa007f5181c5109cabc8378bc271e3',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'luke',
      token_sha256: 'a3829d06755b1d1d975569b98ce84067f2d161a29ae41b594170974e29348f33',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'guy',
      token_sha256: 'd20e653ab09192c0f9673d05de7f184701612858b0b59f39424d357802b0e1f6',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'arunchaganty',
      token_sha256: '37584422d61dc3015ef8a2df199887dbf50c3ad158024f6bb6ff4aa9fe9d7bf4',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'carl',
      token_sha256: '6bf68e3c5347f5f2061b573ddec33f4482ca7baae6afefbb4796dda832fcb812',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'dirk',
      token_sha256: 'f86853dde92f4ec233ca902c37547002f85e2dde90d54c37fbb75aeb8617477d',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'dietz',
      token_sha256: '9f0b4053cefce96add05be7bb9e3b5d17eebc0d1ed1e8fe8fd78e8ac6558e6f3',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'igoros',
      token_sha256: '39c0146ef50dbeb2dbc1a37bb17436cdf3bdc89bc2a2dcef887d4b7f513813cb',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'vincent',
      token_sha256: '94cf89fb40e6dbc4ef8fee9db6ffbfe28fb2d86a57126bc5ea6271228d606e02',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'markus',
      token_sha256: 'e4ea860d1ca7e8d873ce7d93581628b6842d196009f48103d4eff62c6cc53542',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'mattgauntseo',
      token_sha256: '3b8248426bdbaf186a7556e91479ff3d4f1218e94c96bfa93b4e291bf82cd541',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'jonengler',
      token_sha256: 'f400cee75c5842e4ec2dfcc2f490eb0f4b5e3067b7283b480e433bc7e8a82a18',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'jacqueline',
      token_sha256: 'd26d66590cf806585c8a0df0cd33de9e5e49d80be90f3b2aa0eb6cd50cfd4c5a',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'mb',
      token_sha256: '0c2bf334d87ecf97b95d647edb637b6ec612568c855535707d3f333f16a65a4b',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'liam',
      token_sha256: '0e4faa04a75b4d0a1d42d7c99336cb2652a3bc5d98612151837efd01ec2ca8ad',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'moogi',
      token_sha256: '6a4db4a3e8b761272df23a263d4a5fa115f15b743db4cd1573c8a2dd5d84e57a',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'zhewei',
      token_sha256: '2296852a62d65ec08bef713582c85585275ee8e478f955e67a174557326e03e7',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'franknunley',
      token_sha256: 'cff2db6a394724566ba6fc3dc505413ef1fd7d3f2cec4b2d21fdfe336b2b74bf',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'zhuoran',
      token_sha256: 'ac80760f934c6a5c18ebd618dc18b58a99184eebb9c367c9cb71c7f4f89d9841',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'aswin',
      token_sha256: '6e28bc735953a597df44911e641c448aa3ba41db3b01349fe4ff96e097060d17',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'joel',
      token_sha256: '5dc9dfd55950e8681582b7b7f394c2723318ea515945759ef6ade153f158894d',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'ran',
      token_sha256: 'a2d47ea6843ed0f35785abcd1de11449f2f4cf21deaea44bd9054fc072f45d2b',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'eval-determined-bot',
      token_sha256: 'c201b610baa383cda5aef91cba23d8c9844e3a7253a97420fe29bb277ebe437a',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'jeff',
      token_sha256: '166128be4f7a0208064bd58bd26ced0de0e16fa6a902afd97bd0a5e94245ab5d',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'eric',
      token_sha256: '2b859355fe260ac1fa9e5a4bbf085317bcb8acb9cd37ec365723b5177bd12310',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'andre',
      token_sha256: 'ad19c9be7c62f6a54e46a7c36e598c8a31673f59cea11fd0ddd794e839119600',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'intern-chat-proto',
      token_sha256: '07851e739a74660fed04d6b97ad4393eaf7404bc80979b3c2383c67c62f27987',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'pranay',
      token_sha256: '609fb2945bb5d10d6d3e1598ee08eeee4eb97bf45bae1ec43c5bf9627fefab76',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'devang',
      token_sha256: 'e4674e23c22fdcaa9f9756c2ccd1b5f43bd7f8bdd9134ae17c9d3ba801041213',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'shawn',
      token_sha256: '32c3ccccc2851def13712aad9146ad38779bf3476ecc8d09c1b5b2b0486380d8',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'surbhi',
      token_sha256: 'e5b755c3bf930880b1efdb6013576570b6352964b8f3dda5052a7cf4b23d3e4a',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'brandon',
      token_sha256: 'a5f39ea021299664f8f82ff916a7c06d8410b58d1f43cc5a4259aea19d0772e8',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'des',
      token_sha256: '736130583d97715a21b4314e4b26571a05aa2090d5f35ada6cd2a580d530f470',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'navtej',
      token_sha256: 'ca91f224b72239934406df13e448854e3d1209577246dbe279303242fdd54496',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'nikita',
      token_sha256: '8b6f2b34d66e877412744383eeddb9a7e522119636368f61496da2d0755dd60b',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'mattm',
      token_sha256: 'fe50157d5132a1ceb031b2c1b8dc43d9d43db5e85eb9aca9f25efb008528cfe1',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'mpauly',
      token_sha256: 'd31d499349f197a1e90798cc734ae8115eb4f39ec4b546fe998877f5d68674ad',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'edvin',
      token_sha256: 'c93f7ec01581a121c411c29af029580c78803c27c9e910504865395683e20678',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'insprd',
      token_sha256: '9543e1487f15a441ed3210c2b30f56d74a9afe4d5e8e768b2d836f134c66dc72',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'eval-external-context-bot',
      token_sha256: '32aa541dad87c5caf6b7b35fe1acfbf1ec8cdc20dfae04b396d8d4592e1abb3c',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'matts',
      token_sha256: 'ccc782987944fa62ddc551ab219ed27b0b2fee964f9deca8477aed6eb17565bb',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'tamuz',
      token_sha256: '858f1320325442c9cbbf88f66edc60115ef245fad1ee1710da464a5521e9edef',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'lior',
      token_sha256: '0a6f12e30b93d1f9a19233c76b919e360a469bcb86e2605966c6904a3b8047a3',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'tongfei',
      token_sha256: '78e0170f2529491677591490df5d21539b9224013d69ce02dfeb972700dca693',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'diane',
      token_sha256: 'b02699eb89ce3f77df3d616256e3045cb36944e6aa51f55b06340a18a95ac9b3',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'jared',
      token_sha256: 'd05410347cb2509ea38ac856c1fc8b44c16f98d8379a4f4cc507cbfbcd37fdb6',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'testrunner',
      token_sha256: '9bbffc0473f118d453823c74295d1161a0d30060929f00cf83af18415e16ab72',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'itamar',
      token_sha256: '26a39bfdca96d20e33a1fa35c57740a59d653bea04696717db88a38895dcfd62',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'vaibhav',
      token_sha256: 'a1dda8de27941a8565f566a115c5dad095f7f274f23a077af85d7d256f2adc90',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'vinay',
      token_sha256: 'f3b541a5b414c79009aeca1c4115efcbb67ef5d0e3bf8264a560c122a114234a',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'vpas',
      token_sha256: '8a0c59c7b608812235fe4770b5f86533215dd81ae5a6acc4fc3004a5587ceb5f',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'cam',
      token_sha256: 'd01f6425b8cf3d337c9ebbd91cb761bf8fdd7e3aeed2ea25545a01777da9d7e9',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'siyao',
      token_sha256: 'b9d9bc9eb17f82c515095f85b5aaf8ae39aab2c9a809e60b1a87a411bf62d61d',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'zheren',
      token_sha256: 'bb2159dba4ae44a9e787b0f3d98b88eda4ce9c0fca1c07c8bc89694666027f4a',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'maxhahn',
      token_sha256: 'd5c39d1d78e6b7226710b86e1685b15fdd04db5f7f0522e72b097baaf3064763',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'tenzin',
      token_sha256: '18d555f71be304aa0198da977f9d224d697e9d515c65871c16d6eaee302f1a7e',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'harry',
      token_sha256: 'dbeccac9f060b676c462e6b05483b85f1242854f135f641e04a2346254448814',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'mike',
      token_sha256: 'd4b015b5297035e3abf4269fb3e9e59468259fb7a8ba258fecd610f1fa9d2d65',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'anshuman',
      token_sha256: 'b8999ae70bbd6b1fdf619ba8497dc4358c6a66a2286f87ded396d61256568c96',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'markp',
      token_sha256: '24df56324262dfc08db6a75150ccbfd97f4ffd8a47f89877f1c8663fb2eeca8e',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'wattenberger',
      token_sha256: 'a74fa0cd770b883ff026fc87b1d32c163b8f7ae892aafe2d9857bad511a90c97',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'bin',
      token_sha256: 'cea73adbbdeb3569e9dcb9449ebbbcc4340a1287a22e4b3fd9f134e702643109',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'justinxu',
      token_sha256: 'df9080a459df0a59d96c9d4f7eb5fa155c03c33c3d442506ceafa3460a24468e',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'osr',  //. Omendra
      token_sha256: 'f7a38fd0fe0de4e5bd7d31b1f7419907462550aed43e30ad5b3ca7cce7471e90',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'kye',
      token_sha256: 'cd51149412e151ca028ae3e1dd18d5b6fc793fa2bcb748335139e4203e57f29c',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'chris',
      token_sha256: '87ef0262e200930fa6514e931abe6f92a9280def70b64f9d63fb0897a19a291e',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'justin',
      token_sha256: '8015cf326446d62511e25b5fc8798f982a723cdc5a3b2b4ccdfee5572df84c57',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'lawrence',
      token_sha256: 'cd9a9a19fa461103e6b1dabdd1007310955301d3648fe7e2ceb72d320a62ef4b',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'nathro',
      token_sha256: 'cdbea79abec0f19092336d0c285a19ea60d552fb7ae010d33449551ba1ed11c6',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'anton',
      token_sha256: 'bbc83eb5dc506c2b81f0ed254e4dd1c2dd9827d23abe7b52ff1aee1469349cf6',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'akshay',
      token_sha256: '40f0744506439db630e479ece12090c37569824beec204beda1184d5a1b8e647',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'jhu',
      token_sha256: '71efc68ef311295b331850df32099538ab22b0cdb9ccf2b4046603195df991fc',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'kpham',
      token_sha256: '9a6a54cab8e01a05d116d56c1823f7e978da40d4652d85d75cd5dd224de4e059',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'triage-bot',
      token_sha256: '5102580eee78d910dba93fbac831a7a81d8708725ec661ed3e67cd00698162d4',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
    {
      user_id: 'release-mgr-bot',
      token_sha256: 'aba94ed036aafa274f4718d2cfb63be85110eb92a8fbc02743bbf8ad222c4ff9',  // pragma: allowlist secret
      tenant_name: tenant_name,
    },
  ]
