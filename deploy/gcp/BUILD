load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_to_json_test",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

kubecfg(
    name = "kubecfg_augment_sysctl",
    src = "augment-sysctl.jsonnet",
    cluster_wide = True,
)

kubecfg(
    name = "kubecfg_bigtable",
    src = "bigtable.jsonnet",
    cluster_wide = True,
    visibility = ["//deploy/common:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:eng-namespaces-lib",
    ],
)

kubecfg(
    name = "kubecfg_spanner_instance",
    src = "spanner_instance.jsonnet",
    cluster_wide = True,
    visibility = ["//deploy/common:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "kubecfg_peering",
    src = "peering.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "kubecfg_config_connector",
    src = "config_connector.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:eng-namespaces-lib",
        "//deploy/common:lib",
    ],
)

kubecfg(
    name = "kubecfg_nvidia_image_keeper",
    src = "nvidia_image_keeper.yaml",
    cluster_wide = True,
)

kubecfg(
    name = "kubecfg_node_groups",
    src = "node_groups.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

kubecfg_library(
    name = "gcp-lib",
    srcs = [
        "gcp-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "kubecfg_iap_namespace",
    src = "iap-namespace.jsonnet",
    cluster_wide = True,
    visibility = ["//visibility:public"],
    deps = [
        ":gcp-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:eng-namespaces-lib",
    ],
)

kubecfg(
    name = "kubecfg_cloud_armor_policies",
    src = "cloud_armor_policies.yaml",
    cluster_wide = True,
)

kubecfg_library(
    name = "monitoring-lib",
    srcs = [
        "monitoring-lib.jsonnet",
    ],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

jsonnet_to_json_test(
    name = "gcp-lib-test",
    src = "gcp-lib-test.jsonnet",
    deps = [":gcp-lib"],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg_augment_sysctl",
        ":kubecfg_bigtable",
        ":kubecfg_cloud_armor_policies",
        ":kubecfg_config_connector",
        ":kubecfg_config_connector_operator-1-133-0",
        ":kubecfg_iap_namespace",
        ":kubecfg_node_groups",
        ":kubecfg_nvidia_image_keeper",
        ":kubecfg_peering",
        ":kubecfg_spanner_instance",
    ],
)

kubecfg(
    name = "kubecfg_config_connector_operator-1-133-0",
    src = ":configconnector-operator-1.133.0.yaml",
    cluster_wide = True,
    norewrite = True,
)
