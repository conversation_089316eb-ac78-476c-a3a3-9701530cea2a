/* eslint-disable @typescript-eslint/no-unsafe-call */
import {
    AgentOnboarding,
    getAgentMemories,
    onOrientationStatusChanged,
    runInitialOrientationWithProgressSingleton,
    sendOrientationStatusToWebviews,
} from "$vscode/src/chat/agent-onboarding-orientation";
import { TerminalLaunchProcessTool } from "$vscode/src/chat/terminal-tools";
import { AssetManager } from "$vscode/src/client-interfaces/asset-manager";
import {
    ChatHomeWebviewState,
    SHARED_AGENT_STORE_NAME,
} from "$vscode/src/webview-panels/remote-agents/common-webview-store";
import { GitReferenceMessenger } from "$vscode/src/webview-panels/remote-agents/git-reference-messenger";
import { RemoteAgentDiffMessenger } from "$vscode/src/webview-panels/remote-agents/remote-agent-diff-messenger";
import { RemoteAgentsMessenger } from "$vscode/src/webview-panels/remote-agents/remote-agents-messenger";
import { SharedWebviewStore } from "$vscode/src/webview-panels/stores/shared-webview-store";
import { ToolConfigStore } from "$vscode/src/webview-panels/stores/tool-config-store";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import { SourceFolderType } from "$vscode/src/workspace/workspace-types";

import {
    TestResponse,
    TestResponseSchema,
    TestService,
} from "@augment-internal/sidecar-libs/protos/test_service_pb";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import {
    ChatMode,
    ChatRequestNodeType,
    Exchange,
    Rule,
    TerminalInfo,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { RulesService } from "@augment-internal/sidecar-libs/src/chat/rules-service";
import { trackEvent } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import { ChatResult } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { sendMessageToSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { APIError, getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { ISidecarDisposable } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-types";
import { InitialOrientationCaller } from "@augment-internal/sidecar-libs/src/metrics/types";
import { getDefaultShell } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import { SimpleShellTool } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/simple-shell";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { withTimeout } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_GUIDELINES_FILE,
    AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import { APIStatus, ErrorCode } from "@augment-internal/sidecar-libs/src/utils/types";
import { UntruncatedContentManager } from "@augment-internal/sidecar-libs/src/utils/untruncated-content-manager";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { SendMessageTransport } from "@augment-internal/sidecar-libs/transport/grpc/ts/send-message-transport";
import { ServiceRegistry } from "@augment-internal/sidecar-libs/transport/grpc/ts/service-registry";
import { create } from "@bufbuild/protobuf";
import debounce from "lodash/debounce";
import throttle from "lodash/throttle";
import path from "path";
import * as vscode from "vscode";

import { APIServer, ChatInstructionStreamResult, Model } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import ChatModel, { PreferenceState } from "../../chat/chat-model";
import { AugmentChatEntry } from "../../chat/chat-types";
import { editorToFileRangesSelected, pathNameToFileDetails } from "../../chat/data-transform-utils";
import { FuzzyFsSearcher } from "../../chat/fuzzy-fs-searcher";
import { FuzzySymbolSearcher } from "../../chat/fuzzy-symbol-searcher";
import { GuidelinesWatcher } from "../../chat/guidelines-watcher";
import { RulesWatcher } from "../../chat/rules-watcher";
import { ISmartPasteCacheContext, SmartPasteCache } from "../../chat/smart-paste-cache";
import { FocusAugmentPanel } from "../../commands/focus-augment-panel";
import { ConflictingExtensions } from "../../conflicting-extensions";
import { VSCodeDiffViewDocument } from "../../diff-view/document-vscode";
import { SessionOrigin } from "../../diff-view/session-reporter";
import { FeatureFlagManager } from "../../feature-flags";
import { getLogger } from "../../logging";
import { type ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";
import { ChatMetricName, WebviewName } from "../../metrics/types";
import { WorkTimer } from "../../metrics/work-timer";
import { NotificationWatcher } from "../../notification-watcher";
import { OnboardingSessionEventName } from "../../onboarding/onboarding-types";
import { ResolveFileService } from "../../resolve-file-service";
import { base64ToBytes, bytesToBase64 } from "../../utils/base64-utils";
import { CommitMessagePromptPreparer } from "../../utils/commit-message-tools/commit-message-prompt-preparer";
import { AugmentGlobalState, GlobalContextKey } from "../../utils/context";
import { DisposableService } from "../../utils/disposable-service";
import { expandSelectionsToLineBoundaries, getSelectedCodeDetails } from "../../utils/editor";
import { toEditStream } from "../../utils/edits/diff-by-line";
import {
    getExtensionPatchNumber,
    isExtensionVersionGte,
    isVsCodeVersionGte,
} from "../../utils/environment";
import { KeybindingWatcher } from "../../utils/keybindings";
import { dirName, isAbsolutePathName } from "../../utils/path-utils";
import {
    CommitMessagePromptData,
    FileType,
    KeyValuePair,
    SelectedCodeDetails,
    SystemStateName,
    SystemStatus,
} from "../../utils/types";
import { AsyncMsgHandler } from "../../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../../utils/webviews/messaging-helper";
import { openDiffInBuffer } from "../../utils/webviews/open-diff-in-buffer";
import { openFileFromMessage } from "../../utils/webviews/open-file";
import { DiffViewPanelOptions, DiffViewWebviewPanel } from "../../webview-panels/diff-view-panel";
import { PreferenceWebviewPanel } from "../../webview-panels/preference-panel";
import { Pair } from "../../webview-panels/preference-panel-types";
import {
    CallToolMessage,
    CallToolResponse,
    CancelToolRunMessage,
    CancelToolRunResponse,
    CanShowTerminalRequest,
    CanShowTerminalResponse,
    ChatClearMetadata,
    ChatClearMetadataData,
    ChatDeleteImageRequest,
    ChatDeleteImageResponse,
    ChatInitialize,
    ChatLoadImageRequest,
    ChatLoadImageResponse,
    ChatModeChangedMessage,
    ChatModelReply,
    ChatSaveAttachmentRequest,
    ChatSaveAttachmentResponse,
    ChatSaveImageRequest,
    ChatSaveImageResponse,
    ChatUserCancel,
    ChatUserMessage,
    CheckAgentAutoModeApprovalResponse,
    CheckToolExistsRequest,
    CheckToolExistsResponse,
    ConfirmationModalResponse,
    EmptyMessage,
    ExternalSource,
    FileDetails,
    FindExternalSourcesRequest,
    FindExternalSourcesResponse,
    GenerateCommitMessage,
    GetChatRequestIdeStateRequest,
    GetChatRequestIdeStateResponse,
    GetSubscriptionInfoRequest,
    GetSubscriptionInfoResponse,
    GetWorkspaceInfoResponse,
    ISearchScopeArgs,
    MainPanelApp,
    OpenConfirmationModal,
    ReportWebviewClientMetricRequest,
    ResolveFileRequest,
    ResolveFileResponse,
    SetAgentAutoModeApproved,
    ShowTerminalRequest,
    ShowTerminalResponse,
    TrackAnalyticsEventRequest,
    WebViewMessage,
    WebViewMessageType,
    WorkspaceFileChunk,
} from "../../webview-providers/webview-messages";
import { OnboardingWorkspaceModel } from "../../workspace/onboarding-workspace-model";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";
import { SyncingStatusReporter } from "../../workspace/syncing-status-reporter";
import {
    IQualifiedPathInfo,
    ISourceFolderInfo,
    pathNameToAbsPath,
    SyncingStatus,
} from "../../workspace/types";
import { viewTextDocument } from "../../workspace/view-text-document";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import { ActionsModel, DerivedState } from "../action-cards/actions-model";
import { DerivedStateName } from "../action-cards/types";
import { MainPanelAppController } from "../main-panel-app-controller";

const MAX_PREFIX_SUFFIX_CHARS = 200000;

export type ChatExtensionMessage = string | NewThreadMessage;

export const chatExtensionMessage = {
    runSlashFix: "runSlashFix",
    newThread: "newThread",
    runSlashExplain: "runSlashExplain",
    runSlashTest: "runSlashTest",
    runSlashDocument: "runSlashDocument",
    resetAgentOnboarding: "reset-agent-onboarding",
} as const;

export interface NewThreadMessage {
    type: typeof chatExtensionMessage.newThread;
    mode?: string;
}

interface ChatParams {
    message: ChatUserMessage;
    requestId: string;
    blobs: Blobs;
    userGuidedBlobs: string[];
    selectedCodeDetails: SelectedCodeDetails | undefined | null;
}

interface ComparisonConfig {
    modelId: string;
    modelIdB: string;
    externalSourceIds: string[];
    externalSourceIdsB: string[];
    implicitExternalSources?: ExternalSource[];
}

export class ChatApp extends DisposableService implements MainPanelAppController {
    private _logger = getLogger("ChatApp");
    private _asyncMsgHandler: AsyncMsgHandler | undefined;
    private _lastKnownSourceFolders: ISourceFolderInfo[] = [];
    private _webview: vscode.Webview | undefined;

    private _smartPasteCache: SmartPasteCache;
    private _commitMessagePromptPreparer: CommitMessagePromptPreparer;
    private _memoryUpdateManagerDisposable?: ISidecarDisposable;

    private _remoteAgentsMessenger: RemoteAgentsMessenger;
    private _gitReferenceMessenger: GitReferenceMessenger;
    private _remoteAgentDiffMessenger: RemoteAgentDiffMessenger;
    private _sharedStore: SharedWebviewStore<ChatHomeWebviewState>;
    private _rulesService: RulesService;

    constructor(
        private readonly _chatModel: ChatModel,
        private readonly _chatModelConfigs: Map<String, Model>,
        private readonly _apiServer: APIServer,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _keybindingWatcher: KeybindingWatcher,
        private readonly _config: AugmentConfigListener,
        private readonly _extensionUri: vscode.Uri,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _clientMetricsReporter: ClientMetricsReporter,
        private readonly _actionsModel: ActionsModel,
        private readonly _syncingEnabledTracker: SyncingEnabledTracker,
        private readonly _onboardingWorkspaceModel: OnboardingWorkspaceModel,
        private readonly _syncingStatus: SyncingStatusReporter,
        private readonly _onboardingSessionEventReporter: OnboardingSessionEventReporter,
        private readonly _fuzzyFsSearcher: FuzzyFsSearcher,
        private readonly _fuzzySymbolSearcher: FuzzySymbolSearcher,
        private readonly _toolsModel: ToolsModel,
        private readonly _resolveFileService: ResolveFileService,
        private readonly _checkpointManager: AggregateCheckpointManager,
        private readonly _guidelinesWatcher: GuidelinesWatcher,
        private readonly _rulesWatcher: RulesWatcher,
        private readonly _assetManager: AssetManager,
        private readonly _globalState: AugmentGlobalState,
        private readonly _workTimer: WorkTimer,
        private readonly _toolConfigStore: ToolConfigStore,
        private readonly _extensionContext: vscode.ExtensionContext,
        private readonly _notificationWatcher: NotificationWatcher | undefined
    ) {
        super();

        this._commitMessagePromptPreparer = new CommitMessagePromptPreparer();

        // Remote agent message handlers
        this._remoteAgentsMessenger = new RemoteAgentsMessenger(
            this._apiServer,
            this._extensionUri,
            this._workTimer,
            this._globalState,
            this._toolConfigStore,
            this._config,
            this._guidelinesWatcher,
            this._workspaceManager,
            this._extensionContext,
            this._featureFlagManager
        );
        this.addDisposable(this._remoteAgentsMessenger);
        this._gitReferenceMessenger = new GitReferenceMessenger(this._apiServer, this._config);
        this._remoteAgentDiffMessenger = new RemoteAgentDiffMessenger(this._apiServer);
        this._sharedStore =
            SharedWebviewStore.getStore<ChatHomeWebviewState>(SHARED_AGENT_STORE_NAME);
        this._rulesService = new RulesService();
        this.addDisposable(this._rulesService);
        this._smartPasteCache = new SmartPasteCache(async (ctx: ISmartPasteCacheContext) => {
            const requestId = this._apiServer.createRequestId();
            return Promise.resolve({
                generator: this._smartPasteWithChatInstruction(ctx, requestId),
                requestId,
            });
        });
        this.addDisposables(
            this._actionsModel.onDerivedStatesSatisfied(this.handleDerivedStateChange.bind(this)),
            this._syncingEnabledTracker.onDidChangeSyncingEnabled(
                this.sendSyncEnabledStatus.bind(this)
            ),
            this._guidelinesWatcher.onDidChange(this.sendCurrentGuidelinesState.bind(this)),
            this._rulesWatcher.onDidChange(this.sendRulesUpdate.bind(this)),
            this._smartPasteCache,
            this._checkpointManager.onAgentEditListHasUpdates(() =>
                this._notifyAgentEditListHasUpdates()
            ),
            this._toolsModel.onRestartHosts(() => {
                this.setupMemoryUpdateListener();
            })
        );

        // Register with the memory update manager if available
        this.setupMemoryUpdateListener();
    }

    appType(): MainPanelApp {
        return MainPanelApp.chat;
    }

    title(): string {
        return "";
    }

    register(webview: vscode.Webview): void {
        this._webview = webview;

        // Register a handler to receive async messages from the webview
        this._asyncMsgHandler = createAsyncMsgHandlerFromWebview(this._webview, this._workTimer);
        this.addDisposable(this._asyncMsgHandler);

        const messengerId = "chat-webview-app";
        if (this._asyncMsgHandler) {
            this._sharedStore.subscribe(messengerId, this._webview, this._asyncMsgHandler);
            this.addDisposable(
                new vscode.Disposable(() => {
                    if (this._asyncMsgHandler) {
                        this._sharedStore.unsubscribe(messengerId);
                    }
                })
            );
        }

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findFileRequest,
            this._fuzzyFsSearcher.findFiles
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findFolderRequest,
            this._fuzzyFsSearcher.findFolders
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findRecentlyOpenedFilesRequest,
            this._fuzzyFsSearcher.findRecentFiles
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.resolveFileRequest,
            async (m: ResolveFileRequest): Promise<ResolveFileResponse> => {
                return {
                    type: WebViewMessageType.resolveFileResponse,
                    data: await this.resolveTargetPath(
                        m.data.relPath,
                        m.data.searchScope,
                        m.data.exactMatch
                    ),
                };
            }
        );
        this._asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.chatUserMessage,
            this.onUserSendMessage.bind(this)
        );

        this._asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.generateCommitMessage,
            this.generateCommitMessage.bind(this)
        );

        this._asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.chatGetStreamRequest,
            this._chatModel.getChatStreamFromMessage.bind(this._chatModel)
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatUserCancel,
            (m: ChatUserCancel) => this.onUserCancel(m)
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatRating,
            this._chatModel.sendFeedback
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.saveChat,
            this._chatModel.saveConversation
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.openConfirmationModal,
            (m: OpenConfirmationModal) => this.onOpenConfirmationModal(m)
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findExternalSourcesRequest,
            this.onExternalSourceSearch
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.resolveWorkspaceFileChunkRequest,
            this._chatModel.resolveWorkspaceFileChunk
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findSymbolRequest,
            this._fuzzySymbolSearcher.findSymbols
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findSymbolRegexRequest,
            this._fuzzySymbolSearcher.findSymbolsRegex
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.getDiagnosticsRequest,
            this._chatModel.getDiagnostics
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.reportWebviewClientMetric,
            (m: ReportWebviewClientMetricRequest): Promise<EmptyMessage> => {
                this._clientMetricsReporter.reportWebviewClientMetric(m.data);
                return Promise.resolve({ type: WebViewMessageType.empty });
            }
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.trackAnalyticsEvent,
            (m: TrackAnalyticsEventRequest): Promise<EmptyMessage> => {
                trackEvent(m.data.eventName, m.data.properties);
                return Promise.resolve({ type: WebViewMessageType.empty });
            }
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.getWorkspaceInfoRequest,
            async (): Promise<GetWorkspaceInfoResponse> => {
                await Promise.resolve(null);
                try {
                    const sourceFolders = this._workspaceManager.listSourceFolders();
                    const totalTrackedFileCount = sourceFolders
                        .filter((folder) => folder.syncingEnabled)
                        .reduce((total, folder) => {
                            if ("trackedFileCount" in folder) {
                                return total + folder.trackedFileCount;
                            }
                            return total;
                        }, 0);
                    const trackedFileCount =
                        totalTrackedFileCount > 0 ? [totalTrackedFileCount] : [];

                    return {
                        type: WebViewMessageType.getWorkspaceInfoResponse,
                        data: {
                            trackedFileCount:
                                trackedFileCount.length > 0 ? trackedFileCount : undefined,
                        },
                    };
                } catch (error) {
                    this._logger.error("Error getting workspace info:", error);
                    return {
                        type: WebViewMessageType.getWorkspaceInfoResponse,
                        data: {},
                    };
                }
            }
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.showAugmentPanel,
            this.showAugmentPanel
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.callTool,
            this.callTool,
            false /* logIfSlow */
        );
        this._asyncMsgHandler.registerHandler(WebViewMessageType.cancelToolRun, this.cancelToolRun);
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.checkToolExists,
            this.checkToolExists
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.getChatRequestIdeStateRequest,
            this._getChatRequestIdeState
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatSaveImageRequest,
            this._saveImage
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatSaveAttachmentRequest,
            this._saveAttachment
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatLoadImageRequest,
            this._loadImage
        );
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatDeleteImageRequest,
            this._deleteImage
        );

        // Register handler for chat mode change
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatModeChanged,
            (m: ChatModeChangedMessage) => {
                this._toolsModel.setMode(m.data.mode);
                return Promise.resolve({ type: WebViewMessageType.empty });
            }
        );

        // Add handler for getting agent onboarding prompt
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatGetAgentOnboardingPromptRequest,
            async () => {
                const shellName: string = getDefaultShell(process.platform) as string;
                const shellTool = new SimpleShellTool(ChatMode.agent, process.platform, shellName);
                const agentOnboarding = new AgentOnboarding(shellTool, this._workspaceManager);

                // Report the SawAgentOnboarding event
                this._onboardingSessionEventReporter.reportEvent(
                    OnboardingSessionEventName.SawAgentOnboarding
                );

                return Promise.resolve({
                    type: WebViewMessageType.chatGetAgentOnboardingPromptResponse,
                    data: {
                        prompt: await agentOnboarding.getOnboardingInstructions(),
                    },
                });
            }
        );

        // Add handlers for agent auto mode approval
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.checkAgentAutoModeApproval,
            (): Promise<CheckAgentAutoModeApprovalResponse> => {
                // Get the value from the injected global state
                const isApproved = this._globalState.get<boolean>(
                    GlobalContextKey.agentAutoModeApproved
                );
                return Promise.resolve({
                    type: WebViewMessageType.checkAgentAutoModeApprovalResponse,
                    data: isApproved ?? false,
                });
            }
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.setAgentAutoModeApproved,
            async (message: SetAgentAutoModeApproved) => {
                // Update the value in the injected global state
                await this._globalState.update(
                    GlobalContextKey.agentAutoModeApproved,
                    message.data
                );

                return {
                    type: WebViewMessageType.empty,
                };
            }
        );

        // TEST HOOK FOR DOGFOODING PROTOBUF
        this.addDisposable(registerProtoServices(this._webview));

        // Add handler for getting subscription info
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.getSubscriptionInfo,
            this.getSubscriptionInfo.bind(this)
        );

        // Add handler for checking if terminal can be shown
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.canShowTerminal,
            this.handleCanShowTerminal.bind(this)
        );

        // Add handler for showing terminal
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.showTerminal,
            this.handleShowTerminal.bind(this)
        );

        // When we first load the webview, send the source folders and sync status
        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatLoaded,
            async (_): Promise<ChatInitialize> => {
                this.sendSourceFolders();
                this.sendSyncStatus();
                this.sendSyncEnabledStatus();
                this.sendCurrentGuidelinesState();
                if (this._onboardingWorkspaceModel.shouldShowSummary) {
                    this.sendShowSummary();
                }

                this._actionsModel.broadcastDerivedStates();

                const editor = vscode.window.activeTextEditor;
                if (editor?.document.uri) {
                    this.sendCurrentlyOpenFiles([editor.document.uri]);
                    this.sendFileSelections(editor);
                } else {
                    this.sendCurrentlyOpenFiles([]);
                }

                this._notifyAgentEditListHasUpdates();

                // If there is a memories path available, use it
                const agentMemoriesFilePathName = this._toolsModel.memoriesAbsPath
                    ? {
                          rootPath: "",
                          relPath: this._toolsModel.memoriesAbsPath,
                      }
                    : undefined;
                const enableAgentSwarmMode = await this._toolsModel.getEnableAgentSwarmMode();
                return Promise.resolve({
                    type: WebViewMessageType.chatInitialize,
                    data: {
                        enablePreferenceCollection: this._config.config.preferenceCollection.enable,
                        enableRetrievalDataCollection:
                            this._config.config.preferenceCollection.enableRetrievalDataCollection,
                        enableDebugFeatures: this._config.config.enableDebugFeatures,
                        useRichTextHistory: this._config.config.chat.useRichTextHistory,
                        modelDisplayNameToId: this.getMergedAdditionalChatModels(),
                        fullFeatured: true,
                        enableExternalSourcesInChat: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags
                                .vscodeExternalSourcesInChatMinVersion ?? ""
                        ),
                        smallSyncThreshold:
                            this._featureFlagManager.currentFlags.smallSyncThreshold,
                        bigSyncThreshold: this._featureFlagManager.currentFlags.bigSyncThreshold,
                        enableSmartPaste: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.enableSmartPasteMinVersion ?? ""
                        ),
                        enableDirectApply: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeDirectApplyMinVersion ?? ""
                        ),
                        summaryTitles: this._featureFlagManager.currentFlags.enableSummaryTitles,
                        suggestedEditsAvailable: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""
                        ),
                        enableShareService: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeShareMinVersion ?? ""
                        ),
                        maxTrackableFileCount:
                            this._featureFlagManager.currentFlags.maxTrackableFileCount,
                        enableDesignSystemRichTextEditor: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags
                                .vscodeDesignSystemRichTextEditorMinVersion ?? ""
                        ),
                        enableSources: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeSourcesMinVersion ?? ""
                        ),
                        enableChatMermaidDiagrams:
                            this._featureFlagManager.currentFlags.enableChatMermaidDiagrams,
                        smartPastePrecomputeMode:
                            this._featureFlagManager.currentFlags.smartPastePrecomputeMode,
                        useNewThreadsMenu: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeNewThreadsMenuMinVersion ??
                                ""
                        ),
                        enableEditableHistory: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeEditableHistoryMinVersion ??
                                ""
                        ),
                        enableChatMermaidDiagramsMinVersion: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags
                                .vscodeEnableChatMermaidDiagramsMinVersion ?? ""
                        ),
                        enableChatMultimodal: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeChatMultimodalMinVersion ??
                                ""
                        ),
                        enableAgentMode: isExtensionVersionGte(
                            getExtensionPatchNumber() === 0
                                ? (this._featureFlagManager.currentFlags
                                      .vscodeAgentModeMinVersion ?? "")
                                : (this._featureFlagManager.currentFlags
                                      .vscodeAgentModeMinStableVersion ?? "")
                        ),
                        enableRichCheckpointInfo: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags
                                .vscodeRichCheckpointInfoMinVersion ?? ""
                        ),
                        agentMemoriesFilePathName,
                        workspaceUnpopulated: this._actionsModel.isDerivedStateSatisfied(
                            DerivedStateName.workspaceNotPopulated
                        ),
                        truncateChatHistory: false, // Need a new version-based flag
                        customPersonalityPrompts: {
                            agent: this._config.config.advanced.personalityPrompts?.agent ?? "",
                            prototyper:
                                this._config.config.advanced.personalityPrompts?.prototyper ?? "",
                            brainstorm:
                                this._config.config.advanced.personalityPrompts?.brainstorm ?? "",
                            reviewer:
                                this._config.config.advanced.personalityPrompts?.reviewer ?? "",
                        },
                        enablePersonalities: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodePersonalitiesMinVersion ??
                                ""
                        ),
                        enableRules: this._featureFlagManager.currentFlags.enableRules,
                        enableBackgroundAgents: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags
                                .vscodeBackgroundAgentsMinVersion ?? ""
                        ),
                        enableNewThreadsList:
                            this._featureFlagManager.currentFlags.enableNewThreadsList ?? false,
                        memoryClassificationOnFirstToken:
                            this._featureFlagManager.currentFlags
                                .memoryClassificationOnFirstToken ?? false,
                        enableGenerateCommitMessage: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags
                                .vscodeGenerateCommitMessageMinVersion ?? ""
                        ),
                        enablePromptEnhancer:
                            this._featureFlagManager.currentFlags.enablePromptEnhancer,
                        agentChatModel: this._featureFlagManager.currentFlags.agentChatModel,
                        enableModelRegistry:
                            this._featureFlagManager.currentFlags.enableModelRegistry ?? false,
                        modelRegistry: this._featureFlagManager.currentFlags.modelRegistry,
                        modelInfoRegistry: this._featureFlagManager.currentFlags.modelInfoRegistry,
                        enableTaskList: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.vscodeTaskListMinVersion ?? ""
                        ),
                        enableAgentAutoMode:
                            this._featureFlagManager.currentFlags.enableAgentAutoMode ?? false,
                        clientAnnouncement:
                            this._featureFlagManager.currentFlags.clientAnnouncement ?? "",
                        useHistorySummary: isExtensionVersionGte(
                            this._featureFlagManager.currentFlags.historySummaryMinVersion ?? ""
                        ),
                        historySummaryParams:
                            this._featureFlagManager.currentFlags.historySummaryParams,
                        conversationHistorySizeThresholdBytes:
                            this._featureFlagManager.currentFlags
                                .conversationHistorySizeThresholdBytes,
                        enableExchangeStorage:
                            this._featureFlagManager.currentFlags.enableExchangeStorage,
                        enableToolUseStateStorage:
                            this._featureFlagManager.currentFlags.enableToolUseStateStorage,
                        retryChatStreamTimeouts:
                            this._featureFlagManager.currentFlags.retryChatStreamTimeouts,
                        enableAgentSwarmMode,
                        enableCommitIndexing:
                            this._featureFlagManager.currentFlags.enableCommitIndexing,
                        enableMemoryRetrieval:
                            this._featureFlagManager.currentFlags.enableMemoryRetrieval,
                        enableAgentTabs: this._featureFlagManager.currentFlags.enableAgentTabs,
                        enableGroupedTools:
                            this._featureFlagManager.currentFlags.enableGroupedTools,
                        remoteAgentsResumeHintAvailableTtlDays:
                            this._featureFlagManager.currentFlags
                                .remoteAgentsResumeHintAvailableTtlDays,
                        isVscodeVersionOutdated: (() => {
                            const minVersion =
                                this._featureFlagManager.currentFlags.vscodeMinVersion;
                            return minVersion ? !isVsCodeVersionGte(minVersion) : false;
                        })(),
                        vscodeMinVersion: this._featureFlagManager.currentFlags.vscodeMinVersion,
                        enableParallelTools:
                            this._featureFlagManager.currentFlags.enableParallelTools ?? false,
                        enableAgentGitTracker:
                            this._featureFlagManager.currentFlags.enableAgentGitTracker ?? false,
                        memoriesParams: this._featureFlagManager.currentFlags.memoriesParams ?? {},
                        enableNativeRemoteMcp:
                            this._featureFlagManager.currentFlags.enableNativeRemoteMcp ?? false,
                        nonDismissibleBannerTestTreatment:
                            this._featureFlagManager.currentFlags.nonDismissibleBannerTestTreatment,
                    },
                });
            }
        );

        // Register a sidecar handler for messages handled by sidecar/libs
        this._asyncMsgHandler.registerSidecarHandler((msg, postMessage) => {
            sendMessageToSidecar(msg, postMessage);
        });

        this._resolveFileService.register(this._asyncMsgHandler);

        /* The below disposables all add callback functions to report workspace status back up to the webview */
        this.addDisposables(
            vscode.window.onDidChangeTextEditorSelection(
                (e: vscode.TextEditorSelectionChangeEvent) => {
                    if (!e || e.textEditor.document.uri.scheme === "output") {
                        // Filtering "output" scheme: work around apparent
                        // VSCode bug where logging to an output channel that is
                        // open in the IDE at startup may cause this event to fire
                        // until a different output panel is selected.
                        //
                        // We already cannot include context from output panel, because
                        // editorToFileRangesSelected() will resolve "output" uri to
                        // an empty request, so no loss of functionality.
                        return;
                    }
                    this.sendSourceFolders();
                    this.sendFileSelections(e.textEditor);
                }
            ),
            this._onboardingWorkspaceModel.onShouldShowSummary(this.sendShowSummary),
            this._webview.onDidReceiveMessage(this.onDidReceiveMessage),
            this._workspaceManager.onDidChangeSourceFolders(this.sendSourceFolders),
            vscode.window.onDidChangeActiveTextEditor((e: vscode.TextEditor | undefined) => {
                this.sendSourceFolders();
                if (e) {
                    this.sendCurrentlyOpenFiles([e.document.uri]);
                } else {
                    this.sendCurrentlyOpenFiles([]);
                    this.sendFileSelections(undefined);
                }
            }),
            this._syncingStatus.onDidChangeSyncingStatus(this.sendSyncStatus),
            onOrientationStatusChanged((status) => {
                // When orientation status changes, send the status to the webview
                void this._webview?.postMessage({
                    type: WebViewMessageType.orientationStatusUpdate,
                    data: status,
                });
            })
        );

        /** Register handlers for messages related to remote agents */
        this._remoteAgentsMessenger.register(this._asyncMsgHandler, this._webview);
        this._gitReferenceMessenger.register(this._asyncMsgHandler);
        this._remoteAgentDiffMessenger.register(this._asyncMsgHandler);

        this.updateLastKnownSourceFolders();
    }

    private async *generateCommitMessage(
        _message: GenerateCommitMessage
    ): AsyncGenerator<ChatModelReply> {
        let prompt;
        try {
            const workspacePath = vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath ?? "";
            const fileDetails = this._resolvePathName(vscode.Uri.file(workspacePath));
            const repoRoot = fileDetails?.repoRoot ?? workspacePath;
            prompt = await this._commitMessagePromptPreparer.getCommitMessagePromptData(repoRoot, {
                diffBudget: 9216,
                messageBudget: 3072,
                relevantMessageSubbudget: 1024,
                diffNoopLineLimit: 5000,
                onlyUseStagedChanges: false,
                maxExampleCommitMessages: 3,
            });
        } catch (e) {
            let errorMessage = `Cannot generate commit message prompt: Failed to generate diff details. Error: ${e instanceof Error ? e.message : String(e)}`;
            void vscode.window.showInformationMessage(errorMessage);
            throw new Error(errorMessage);
        }
        yield* this.onGenerateCommitMessage(prompt);
    }

    private getMergedAdditionalChatModels = (): {
        [key: string]: string | null;
    } => {
        let modelDisplayNameToId = this._config.config.chat.modelDisplayNameToId || {};
        let rawAdditionalChatModels = this._featureFlagManager.currentFlags.additionalChatModels;
        rawAdditionalChatModels = rawAdditionalChatModels.replace(/'/g, '"');
        try {
            let additionalChatModelsDct = (
                rawAdditionalChatModels !== "" ? JSON.parse(rawAdditionalChatModels) : {}
            ) as { [key: string]: string };
            return { ...additionalChatModelsDct, ...modelDisplayNameToId };
        } catch (e) {
            this._logger.debug(`Failed to parse additional chat models: ${(e as Error).message}`);
            return modelDisplayNameToId;
        }
    };

    private onUserCancel = (_message: ChatUserCancel): EmptyMessage => {
        this._logger.debug("onUserCancel");
        this._chatModel.cancelChatStream(_message.data.requestId);
        return {
            type: WebViewMessageType.empty,
        };
    };

    /**
     * Checks if a file is the Agent Memories file.
     *
     * @param fileDetails - The file details to check
     * @returns True if the file is the Agent Memories file, false otherwise
     */
    private isMemoriesFile(fileDetails: FileDetails | IQualifiedPathName): boolean {
        const memoriesAbsPath = this._toolsModel.memoriesAbsPath;
        if (!memoriesAbsPath) {
            return false;
        }

        // Convert file details to QualifiedPathName
        const filePath = QualifiedPathName.from({
            rootPath: "rootPath" in fileDetails ? fileDetails.rootPath : fileDetails.repoRoot,
            relPath: "relPath" in fileDetails ? fileDetails.relPath : fileDetails.pathName,
        });

        // Create QualifiedPathName for memories file
        // The memories path is an absolute path, so we use empty string for rootPath
        const memoriesPath = QualifiedPathName.from({
            rootPath: "",
            relPath: memoriesAbsPath,
        });

        // Use QualifiedPathName.equals for comparison
        return QualifiedPathName.equals(filePath, memoriesPath);
    }

    /**
     * Filters out the Agent Memories file from selected code details.
     *
     * @param details - The selected code details to filter
     * @returns The filtered selected code details, or null if it was the memories file
     */
    private filterMemoriesFromSelectedCode(
        details: SelectedCodeDetails | undefined | null
    ): SelectedCodeDetails | undefined | null {
        if (!details) {
            return details;
        }

        // Check if the path is the memories file
        const isMemories = this.isMemoriesFile({
            rootPath: "",
            relPath: details.path,
        });

        // Return null if it's the memories file, otherwise return the details
        return isMemories ? null : details;
    }

    private sendCurrentlyOpenFiles = debounce((uris: vscode.Uri[]): void => {
        // Get resolved file details
        const fileDetails = uris
            .map((uri) => this._resolvePathName(uri))
            .filter(
                (details: FileDetails | undefined): details is FileDetails => details !== undefined
            )
            // Filter out the memories file
            .filter((details) => !this.isMemoriesFile(details));

        void this._webview?.postMessage({
            type: WebViewMessageType.currentlyOpenFiles,
            data: fileDetails,
        });
    }, 250 /* 250ms debounce */);

    private sendShowSummary = throttle(
        (): void => {
            void this._webview?.postMessage({
                type: WebViewMessageType.shouldShowSummary,
            });
            // Do not show it again unless a new workspace is added
            this._onboardingWorkspaceModel.setShouldShowSummary(false);
            this._onboardingSessionEventReporter.reportEvent(OnboardingSessionEventName.SawSummary);
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private sendSyncStatus = throttle(
        (): void => {
            // Store the last event so any future chat initialization
            // will have the latest value.
            void this._webview?.postMessage({
                type: WebViewMessageType.sourceFoldersSyncStatus,
                data: this._syncingStatus.status,
            });
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private sendSyncEnabledStatus = throttle(
        (): void => {
            void this._webview?.postMessage({
                type: WebViewMessageType.syncEnabledState,
                data: this._syncingEnabledTracker.syncingEnabledState,
            });
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private sendRulesUpdate = throttle(
        () => {
            withTimeout(
                this._rulesService.loadRules({ includeGuidelines: false }),
                60000 // 1 minute
            )
                .then((rules) => {
                    void this._webview?.postMessage({
                        type: WebViewMessageType.getRulesListResponse,
                        data: rules,
                    });
                })
                .catch((error) => {
                    this._logger.error(`Failed to load rules: ${String(error)}`);
                });
        },
        5000 /* 5s throttle */,
        { leading: true, trailing: true }
    );

    private sendCurrentGuidelinesState = throttle(
        (): void => {
            const guidelinesStates = this._guidelinesWatcher.getGuidelinesStates();
            void this._webview?.postMessage({
                type: WebViewMessageType.updateGuidelinesState,
                data: guidelinesStates,
            });
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private sendSourceFolders = throttle(
        (): void => {
            this.updateLastKnownSourceFolders();
            void this._webview?.postMessage({
                type: WebViewMessageType.sourceFoldersUpdated,
                data: { sourceFolders: this._lastKnownSourceFolders },
            });
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private updateLastKnownSourceFolders(): void {
        this._lastKnownSourceFolders = this._workspaceManager.trackedSourceFolderNames();
    }

    private sendFileSelections = throttle(
        (editor: vscode.TextEditor | undefined): void => {
            if (editor?.document.uri) {
                const pathName = this._resolvePathName(editor.document.uri);

                // Only send the current file if it's not the memories file
                if (pathName && !this.isMemoriesFile(pathName)) {
                    void this._webview?.postMessage({
                        type: WebViewMessageType.currentlyOpenFiles,
                        data: [pathName],
                    });
                } else {
                    void this._webview?.postMessage({
                        type: WebViewMessageType.currentlyOpenFiles,
                        data: [],
                    });
                }

                // Only send file selections if it's not the memories file
                if (pathName && !this.isMemoriesFile(pathName)) {
                    void this._webview?.postMessage(
                        editorToFileRangesSelected(editor, this._workspaceManager)
                    );
                } else {
                    // If it's the memories file, send an empty selection
                    void this._webview?.postMessage({
                        type: WebViewMessageType.fileRangesSelected,
                        data: [],
                    });
                }
            } else {
                // If no active editor, send an empty selection
                void this._webview?.postMessage({
                    type: WebViewMessageType.fileRangesSelected,
                    data: [],
                });
            }
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private onOpenConfirmationModal = async (
        message: OpenConfirmationModal
    ): Promise<ConfirmationModalResponse> => {
        const result = await vscode.window.showInformationMessage(
            message.data.message,
            { modal: true }, // Options
            { title: message.data.cancelButtonText, isCloseAffordance: true },
            { title: message.data.confirmButtonText }
        );
        return {
            type: WebViewMessageType.confirmationModalResponse,
            data: {
                ok: result?.title === message.data.confirmButtonText,
            },
        };
    };

    private _handleCompleteChatReply(text: string): void {
        // Record the chat response so it becomes available to completions
        this._workspaceManager.recordChatReponse(text);
    }

    private async postStreamChunksToPanel(
        preferencePanel: PreferenceWebviewPanel,
        text: string,
        requestId: string,
        stream: "A" | "B"
    ): Promise<void> {
        await preferencePanel.postStreamChunk(
            {
                type: WebViewMessageType.chatModelReply,
                data: {
                    text,
                    requestId,
                    workspaceFileChunks: [],
                },
            },
            stream
        );
    }

    private getNextLine(text: string): [string, string] {
        const newlineIndex = text.indexOf("\n");
        if (newlineIndex === -1) {
            return [text, ""];
        }
        return [text.slice(0, newlineIndex + 1), text.slice(newlineIndex + 1)];
    }

    private async *onGenerateCommitMessage(
        promptData: CommitMessagePromptData
    ): AsyncGenerator<ChatModelReply> {
        const requestId = this._apiServer.createRequestId();

        const response = await this._apiServer.generateCommitMessageStream(requestId, promptData);

        for await (const chunk of response) {
            yield {
                type: WebViewMessageType.chatModelReply,
                data: {
                    text: chunk.text,
                    requestId: requestId,
                    workspaceFileChunks: [],
                },
            };
        }
    }

    private handleCanShowTerminal = (message: CanShowTerminalRequest): CanShowTerminalResponse => {
        const { terminalId, command } = message.data;
        const launchProcessTool = this._toolsModel.getTool(LocalToolType.launchProcess);
        let canShow = false;

        if (launchProcessTool && launchProcessTool instanceof TerminalLaunchProcessTool) {
            if (terminalId !== undefined) {
                // Use terminal ID if provided
                canShow = launchProcessTool.processTools.canShowTerminal(terminalId);
            } else if (command !== undefined) {
                // Use command string to find terminal
                canShow = launchProcessTool.processTools.canShowTerminalByCommand(command);
            }
        }

        return {
            type: WebViewMessageType.canShowTerminalResponse,
            data: { terminalId, command, canShow },
        };
    };

    private handleShowTerminal = (message: ShowTerminalRequest): ShowTerminalResponse => {
        const { terminalId, command } = message.data;
        const launchProcessTool = this._toolsModel.getTool(LocalToolType.launchProcess);
        let success = false;

        if (launchProcessTool && launchProcessTool instanceof TerminalLaunchProcessTool) {
            if (terminalId !== undefined) {
                // Use terminal ID if provided
                success = launchProcessTool.processTools.showTerminal(terminalId);
                if (!success) {
                    this._logger.info(`Failed to show terminal with ID ${terminalId}`);
                }
            } else if (command !== undefined) {
                // Use command string to find and show terminal
                success = launchProcessTool.processTools.showTerminalByCommand(command);
                if (!success) {
                    this._logger.info(`Failed to show terminal with command ${command}`);
                }
            }
        } else {
            this._logger.warn(`Launch process tool not found or not the correct type`);
        }

        return {
            type: WebViewMessageType.showTerminalResponse,
            data: { terminalId, success },
        };
    };

    private _chatReplyFromAPIError(requestId: string, e: APIError): ChatModelReply {
        let message = e.message;
        const isChatRetriable =
            e.status === APIStatus.unavailable ||
            e.status === APIStatus.resourceExhausted ||
            e.status === APIStatus.augmentClientTimeout;
        const backoff = isChatRetriable && e.status !== APIStatus.augmentClientTimeout;

        if (e.errorDetails && e.errorDetails.message) {
            // Some special cases which we can improve from today
            switch (e.errorDetails?.code) {
                case ErrorCode.PROMPT_LENGTH_EXCEEDED:
                    // Future: suggest summarizing the conversation if this is an option
                    message =
                        "Internal error: prompt length exceeded. If this condition persists, try starting a new conversation";
                    break;
                case ErrorCode.INPUT_COMPONENT_LENGTH_EXCEEDED:
                    // The detail is expected to be useful here to know what part of the input is too large
                    message = `${e.errorDetails.message}\n${e.errorDetails.detail}`;
                    break;
                case ErrorCode.INVALID_TOOL_USE_HISTORY:
                    message =
                        "Conversation state error. If this condition persists, try starting a new conversation.";
                    break;
                case ErrorCode.DUPLICATE_TOOL_NAMES:
                case ErrorCode.INVALID_TOOL_DEFINITION:
                    message = `${e.errorDetails.message}\n${e.errorDetails.detail || "Check MCP Server(s)"}`;
                    break;
                default:
                    message = e.errorDetails.message;
                    break;
            }
        } else if (e.status === APIStatus.augmentTooLarge) {
            // Maintain existing behavior for 413 error, as it's not always wrong...
            message =
                "The selected text exceeds the allowable limit. Please reduce the amount of text and try again";
        }

        /*
        displayErrorMessage is not rendered as markdown; need to either pass this in a field
        that is expected to be a link, or render markdown (possibly overkill)
        if (e.errorDetails?.help_uri) {
            message += `\n(Learn more: [${e.errorDetails.help_uri}](${e.errorDetails.help_uri})`;
        }
        */

        return {
            type: WebViewMessageType.chatModelReply,
            data: {
                text: "",
                requestId,
                workspaceFileChunks: [],
                error: {
                    displayErrorMessage: message,
                    isRetriable: isChatRetriable,
                    shouldBackoff: backoff,
                },
            },
        };
    }

    private async *onUserSendMessage(message: ChatUserMessage): AsyncGenerator<ChatModelReply> {
        const receivedTimestamp = Date.now();
        let requestId = message.data.requestIdOverride ?? this._apiServer.createRequestId();
        // Pull out all context
        const workspaceContext = this._workspaceManager.getContext();
        let blobs: Blobs = {
            checkpointId: undefined,
            addedBlobs: [],
            deletedBlobs: [],
        };
        if (
            (message.data.context?.userSpecifiedFiles?.length ||
                message.data.context?.sourceFolders?.length) &&
            !message.data.disableRetrieval
        ) {
            blobs = workspaceContext.blobs;
        }

        let userGuidedBlobs: string[] = [];
        if (message.data.context?.userSpecifiedFiles && !message.data.disableRetrieval) {
            userGuidedBlobs = this._getBlobNames(message.data.context.userSpecifiedFiles);
        }

        const userGuidelinesContent = this._guidelinesWatcher.getUserGuidelinesContent();
        const workspaceGuidelinesContent =
            this._guidelinesWatcher.getCurrentWorkspaceGuidelinesContent(this._workspaceManager);

        let externalSourceIds: string[] = [];
        if (message.data.context?.externalSources && !message.data.disableRetrieval) {
            externalSourceIds = message.data.context.externalSources.map((s) => s.id);
        }

        let selectedCodeDetails: SelectedCodeDetails | undefined | null = undefined;
        if (
            (message.data.context?.selections?.length ||
                message.data.context?.recentFiles?.length) &&
            !message.data.disableSelectedCodeDetails
        ) {
            expandSelectionsToLineBoundaries(vscode.window.activeTextEditor);
            const suggestedPrefixCharCount =
                this._chatModelConfigs.get(message.data?.modelId ?? "")?.suggestedPrefixCharCount ??
                MAX_PREFIX_SUFFIX_CHARS;
            const suggestedSuffixCharCount =
                this._chatModelConfigs.get(message.data?.modelId ?? "")?.suggestedSuffixCharCount ??
                MAX_PREFIX_SUFFIX_CHARS;

            selectedCodeDetails = getSelectedCodeDetails(
                vscode.window.activeTextEditor,
                this._workspaceManager,
                suggestedPrefixCharCount,
                suggestedSuffixCharCount
            );

            // Filter out the memories file from selected code details
            selectedCodeDetails = this.filterMemoriesFromSelectedCode(selectedCodeDetails);
        }

        // Handle unknown blobs and checkpoints
        const handleChunkMissingData = (chunk: ChatResult): void => {
            const mgr = this._workspaceManager;
            if (chunk.unknownBlobNames && chunk.unknownBlobNames.length > 0) {
                mgr.handleUnknownBlobs(workspaceContext, chunk.unknownBlobNames);
            }
            if (chunk.checkpointNotFound) {
                void mgr.handleUnknownCheckpoint(requestId, blobs.checkpointId!);
            }
        };

        let chunksReceived = 0;
        let lastChunkTimestamp = 0;
        try {
            if (
                this._config.config.chat.stream !== false &&
                !this._config.config.preferenceCollection.enable
            ) {
                let fullChatReply = "";

                if (message.data.memoriesInfo?.isClassifyAndDistill) {
                    // Determine which prompt to use based on experiment configuration
                    const promptKey =
                        message.data.memoriesInfo.promptKey || "classify_and_distill_prompt";
                    let classifyAndDistillPrompt = this._featureFlagManager.currentFlags
                        .memoriesParams[promptKey] as string;

                    if (!classifyAndDistillPrompt) {
                        this._logger.error(
                            `Classify and distill prompt missing for key: ${promptKey}`
                        );
                        return;
                    }
                    message.data.text = classifyAndDistillPrompt.replace(
                        "{message}",
                        message.data.text
                    );

                    // Apply classify and distill prompt to any text nodes
                    if (message.data.nodes?.length) {
                        message.data.nodes = message.data.nodes.map((node) => {
                            if (node.type === ChatRequestNodeType.TEXT && node.text_node) {
                                return {
                                    ...node,
                                    // eslint-disable-next-line @typescript-eslint/naming-convention
                                    text_node: {
                                        content: classifyAndDistillPrompt.replace(
                                            "{message}",
                                            node.text_node.content
                                        ),
                                    },
                                };
                            }
                            return node;
                        });
                    }
                } else if (message.data.memoriesInfo?.isDistill) {
                    // Deprecated
                    let distillPrompt = this._featureFlagManager.currentFlags.memoriesParams
                        .distill_prompt as string;
                    if (!distillPrompt) {
                        this._logger.error(`Distill prompt missing.`);
                        return;
                    }
                    message.data.text = distillPrompt.replace("{message}", message.data.text);
                }

                let agentMemories = undefined;
                if (this._toolsModel.chatMode === ChatMode.agent) {
                    if (this._featureFlagManager.currentFlags.useMemorySnapshotManager) {
                        // Use the memory snapshot manager to get memories
                        const conversationId = message.data.conversationId ?? "";
                        agentMemories =
                            await this._toolsModel.getAgentMemoriesWithSnapshot(conversationId);
                    } else {
                        agentMemories = await getAgentMemories(
                            this._checkpointManager.getAgentMemoriesAbsPath
                        );
                    }
                }

                // Use rules from message data (loaded in webview) instead of loading them here
                let rules: Rule[] = [];

                if (this._featureFlagManager.currentFlags.enableRules && message.data.rules) {
                    rules = message.data.rules;
                }
                this._logger.debug(`Using ${rules.length} rules from message data`);
                this._logger.debug(`Rules files attached: ${rules.map((r) => r.path).join(",")}`);

                for await (const replyChunk of this._chatModel.chatStream(
                    {
                        requestId,
                        conversationId: message.data.conversationId,
                        message: message.data.text,
                        chatHistory: message.data.chatHistory,
                        blobs,
                        userGuidedBlobs,
                        externalSourceIds,
                        modelId: message.data.modelId,
                        userGuidelines: userGuidelinesContent,
                        workspaceGuidelines: workspaceGuidelinesContent,
                        toolDefinitions: (await this._toolsModel.getToolDefinitions()).map(
                            (tool) => tool.definition
                        ),
                        nodes: message.data.nodes,
                        mode: this._toolsModel.chatMode,
                        agentMemories: agentMemories,
                        personaType: message.data.personaType,
                        rules: rules.map((rule) => ({
                            ...rule,
                            path: `${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/${rule.path}`,
                        })),
                        silent: message.data.silent,
                    },
                    selectedCodeDetails
                )) {
                    // Defined above and created only for this request
                    handleChunkMissingData(replyChunk.data);
                    // For the first chunk, check if we are currently synced.
                    // If we are not synced, prepend a warning message to the
                    // response.
                    if (chunksReceived === 0) {
                        const hasNewlyTracked = this._syncingStatus.status.foldersProgress.some(
                            (p) => p.progress?.newlyTracked
                        );
                        if (
                            this._syncingStatus.status.status !== SyncingStatus.done &&
                            hasNewlyTracked
                        ) {
                            replyChunk.data.text =
                                "*Note: Augment is not yet fully synced and may be unable to answer questions about your workspace.*\n\n" +
                                replyChunk.data.text;
                        }
                    }
                    chunksReceived++;
                    lastChunkTimestamp = Date.now();
                    fullChatReply += replyChunk.data.text;
                    yield replyChunk;
                }
                this._handleCompleteChatReply(fullChatReply);
                return;
            }
            const generator = await this.handlePreferenceCollection(
                message,
                requestId,
                blobs,
                userGuidedBlobs,
                externalSourceIds,
                selectedCodeDetails
            );
            yield* generator;
        } catch (e) {
            const diagnostics: KeyValuePair[] = [
                {
                    key: "message_timestamp_ms",
                    value: `${message.data.createdTimestamp}`,
                },
                {
                    key: "extension_timestamp_ms",
                    value: `${receivedTimestamp}`,
                },
                {
                    key: "error_timestamp_ms",
                    value: `${Date.now()}`,
                },
                {
                    key: "chunks_received",
                    value: `${chunksReceived}`,
                },
                {
                    key: "last_chunk_timestamp_ms",
                    value: `${lastChunkTimestamp}`,
                },
            ];
            let cause = e instanceof Error ? e.cause : undefined;
            for (let causeN = 0; causeN < 3 && cause; causeN++) {
                diagnostics.push({
                    key: `cause_${causeN}`,
                    value:
                        getErrmsg(cause) +
                        (cause instanceof Error && cause.stack ? `\n${cause.stack}` : ""),
                });
                cause = cause instanceof Error ? cause.cause : undefined;
            }
            void this._apiServer.reportError(
                requestId,
                "chat_stream_failed",
                e instanceof Error && e.stack ? e.stack : "",
                diagnostics
            );
            // Additionally, log the error and stacktrace to the console for debugging purposes in case we can't get the message to the BE
            this._logger.error(
                `Chat stream failed: ${String(e)}${e instanceof Error ? `\n${e.stack}` : ""}`
            );
            if (e instanceof APIError) {
                return yield this._chatReplyFromAPIError(requestId, e);
            } else if (e instanceof Error) {
                // For all other errors, ensure we return the request ID with the error
                return yield {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        text: "",
                        requestId: requestId,
                        workspaceFileChunks: [],
                        error: {
                            displayErrorMessage: e.message,
                        },
                    },
                };
            } else if (String(e).startsWith("Error: Cancelled")) {
                // If the user cancelled the message, we do not need to show an error
                return;
            }
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            void vscode.window.showErrorMessage(`Failed to send chat message: ${e}`);
            throw e;
        }
    }

    /**
     * Determines which experimental flow to use for preference collection.
     * The flows are selected based on the following priority and probabilities:
     *
     * 1. High Priority Test (P = 0.75 if previous conditions not met)
     *    - Tests newer model versions against current best
     *    - Uses same external sources for both models
     *
     * 2. Regular Battle (fallback)
     *    - Compares various model configurations
     *    - Includes different context window sizes (16k vs 128k)
     */
    private async handlePreferenceCollection(
        message: ChatUserMessage,
        requestId: string,
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        selectedCodeDetails: SelectedCodeDetails | undefined | null
    ): Promise<AsyncGenerator<ChatModelReply>> {
        // Get model configurations from feature flags
        const eloConfig = this._featureFlagManager.currentFlags.eloModelConfiguration;

        // If no models are provided from backend, don't run comparison
        if (
            ((!eloConfig.highPriorityModels || eloConfig.highPriorityModels.length < 2) &&
                eloConfig.highPriorityThreshold !== 0) ||
            ((!eloConfig.regularBattleModels || eloConfig.regularBattleModels.length < 2) &&
                eloConfig.highPriorityThreshold < 1)
        ) {
            // Return a generator that yields a single response with an error
            return (async function* () {
                await Promise.resolve();
                yield {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        text: "",
                        requestId: requestId,
                        workspaceFileChunks: [],
                        error: {
                            displayErrorMessage: "Model comparison is not available at this time.",
                        },
                    },
                };
            })();
        }

        const highPriorityThreshold = eloConfig.highPriorityThreshold;

        if (
            this._chatModel._preferenceState._selectedCode !== selectedCodeDetails?.selectedCode ||
            this._chatModel._preferenceState._filePath !== selectedCodeDetails?.path
        ) {
            this._chatModel._preferenceState = new PreferenceState();
        }
        const params: ChatParams = {
            message,
            requestId,
            blobs,
            userGuidedBlobs,
            selectedCodeDetails,
        };

        if (Math.random() < highPriorityThreshold) {
            return this.handleHighPriorityTest(
                params,
                externalSourceIds,
                eloConfig.highPriorityModels
            );
        } else {
            return this.handleRegularBattle(
                params,
                externalSourceIds,
                eloConfig.regularBattleModels
            );
        }
    }

    private handleHighPriorityTest(
        params: ChatParams,
        externalSourceIds: string[],
        highPriorityModels: [string, string][] | string[]
    ): Promise<AsyncGenerator<ChatModelReply>> {
        let modelId: string;
        let modelIdB: string;

        // Check if highPriorityModels is an array of pairs or just a flat array of models
        if (highPriorityModels.length > 0 && Array.isArray(highPriorityModels[0])) {
            // It's an array of pairs, select a random pair
            const randomPairIndex = Math.floor(
                Math.random() * (highPriorityModels as [string, string][]).length
            );
            const selectedPair = (highPriorityModels as [string, string][])[randomPairIndex];
            [modelId, modelIdB] = selectedPair;
        } else {
            // It's a flat array of models, select two random models
            [modelId, modelIdB] = (highPriorityModels as string[])
                .sort(() => Math.random() - 0.5)
                .slice(0, 2);
        }

        return Promise.resolve(
            this.runComparison(params, {
                modelId,
                modelIdB,
                externalSourceIds: externalSourceIds,
                externalSourceIdsB: externalSourceIds,
            })
        );
    }

    private handleRegularBattle(
        params: ChatParams,
        externalSourceIds: string[],
        modelOptions: string[]
    ): Promise<AsyncGenerator<ChatModelReply>> {
        // Select two random models from the list
        const modelId = modelOptions[Math.floor(Math.random() * modelOptions.length)];
        let modelIdB;
        do {
            modelIdB = modelOptions[Math.floor(Math.random() * modelOptions.length)];
        } while (modelIdB === modelId && modelOptions.length > 1);

        return Promise.resolve(
            this.runComparison(params, {
                modelId,
                modelIdB,
                externalSourceIds: externalSourceIds,
                externalSourceIdsB: externalSourceIds,
            })
        );
    }

    private async *runComparison(
        params: ChatParams,
        config: ComparisonConfig
    ): AsyncGenerator<ChatModelReply> {
        const requestIdB = this._apiServer.createRequestId();

        const toolDefinitions = (await this._toolsModel.getToolDefinitions()).map(
            (tool) => tool.definition
        );
        const streamA = this._chatModel.chatStream(
            {
                requestId: params.requestId,
                conversationId: params.message.data.conversationId,
                message: params.message.data.text,
                chatHistory: params.message.data.chatHistory,
                blobs: params.blobs,
                userGuidedBlobs: params.userGuidedBlobs,
                externalSourceIds: config.externalSourceIds,
                modelId: config.modelId,
                toolDefinitions: toolDefinitions,
                personaType: params.message.data.personaType,
            },
            params.selectedCodeDetails
        );

        const streamB = this._chatModel.chatStream(
            {
                requestId: requestIdB,
                conversationId: params.message.data.conversationId,
                message: params.message.data.text,
                chatHistory: params.message.data.chatHistory,
                blobs: params.blobs,
                userGuidedBlobs: params.userGuidedBlobs,
                externalSourceIds: config.externalSourceIdsB,
                modelId: config.modelIdB,
                toolDefinitions: toolDefinitions,
                personaType: params.message.data.personaType,
            },
            params.selectedCodeDetails
        );

        const {
            responseA,
            responseB,
            workspaceFileChunksA,
            workspaceFileChunksB,
            preferencePanel,
        } = await this.processStreams(
            streamA,
            streamB,
            params.message,
            params.requestId,
            requestIdB,
            config.implicitExternalSources ? config.implicitExternalSources : [],
            [...(config.externalSourceIds ?? []), ...(config.externalSourceIdsB ?? [])]
        );

        void preferencePanel.postStreamDone();

        const keepA = await preferencePanel?.getResult(
            { a: params.requestId, b: requestIdB } as Pair<string>,
            this._apiServer,
            { a: config.modelId, b: config.modelIdB } as Pair<string>
        );

        if (keepA) {
            this._chatModel._preferenceState._selectedCodeReferenceRequestIdOptionB =
                this._chatModel._preferenceState._selectedCodeReferenceRequestId;
        } else {
            this._chatModel._preferenceState._selectedCodeReferenceRequestId =
                this._chatModel._preferenceState._selectedCodeReferenceRequestIdOptionB;
        }

        this._handleCompleteChatReply(keepA ? responseA : responseB);

        yield {
            type: WebViewMessageType.chatModelReply,
            data: {
                text: keepA ? responseA : responseB,
                requestId: keepA ? params.requestId : requestIdB,
                workspaceFileChunks: keepA ? workspaceFileChunksA : workspaceFileChunksB,
            },
        };
    }

    private async processStreams(
        streamA: AsyncIterable<ChatModelReply>,
        streamB: AsyncIterable<ChatModelReply>,
        message: ChatUserMessage,
        requestId: string,
        requestIdB: string,
        implicitExternalSources: ExternalSource[],
        explicitExternalSources: string[]
    ): Promise<{
        responseA: string;
        responseB: string;
        workspaceFileChunksA: WorkspaceFileChunk[];
        workspaceFileChunksB: WorkspaceFileChunk[];
        preferencePanel: PreferenceWebviewPanel;
    }> {
        let bufferA = "";
        let bufferB = "";
        let responseA = "";
        let responseB = "";
        let workspaceFileChunksA: WorkspaceFileChunk[] = [];
        let workspaceFileChunksB: WorkspaceFileChunk[] = [];

        // Use the new standalone method instead of the chat-dependent one
        const preferencePanel: PreferenceWebviewPanel =
            await PreferenceWebviewPanel.launchStandalonePreferencePanel(
                this._extensionUri,
                {
                    a: new AugmentChatEntry(message.data.text, ""),
                    b: new AugmentChatEntry(message.data.text, ""),
                } as Pair<AugmentChatEntry>,
                this._config.config.preferenceCollection.enableRetrievalDataCollection,
                this._fuzzyFsSearcher,
                implicitExternalSources,
                explicitExternalSources,
                this._workTimer
            );

        const iteratorA = streamA[Symbol.asyncIterator]();
        const iteratorB = streamB[Symbol.asyncIterator]();
        let resultA = await iteratorA.next();
        let resultB = await iteratorB.next();

        while (!resultA.done || !resultB.done) {
            if (!resultA.done && (!bufferA.includes("\n") || resultB.done)) {
                bufferA += resultA.value.data.text;
                workspaceFileChunksA = workspaceFileChunksA.concat(
                    resultA.value.data.workspaceFileChunks
                );
                resultA = await iteratorA.next();
            }
            if (!resultB.done && (!bufferB.includes("\n") || resultA.done)) {
                bufferB += resultB.value.data.text;
                workspaceFileChunksB = workspaceFileChunksB.concat(
                    resultB.value.data.workspaceFileChunks
                );
                resultB = await iteratorB.next();
            }

            let lineA, lineB;
            while (
                (bufferA.includes("\n") && bufferB.includes("\n")) ||
                resultA.done ||
                resultB.done
            ) {
                [lineA, bufferA] =
                    resultA.done && !bufferA.includes("\n")
                        ? [bufferA, ""]
                        : this.getNextLine(bufferA);
                [lineB, bufferB] =
                    resultB.done && !bufferB.includes("\n")
                        ? [bufferB, ""]
                        : this.getNextLine(bufferB);

                responseA += lineA;
                responseB += lineB;

                void this.postStreamChunksToPanel(preferencePanel, responseA, requestId, "A");
                void this.postStreamChunksToPanel(preferencePanel, responseB, requestIdB, "B");

                if (bufferA.length === 0 || bufferB.length === 0) {
                    break;
                }
            }
        }

        // Handle any remaining content in buffers
        if (bufferA) {
            responseA += bufferA;
            void this.postStreamChunksToPanel(preferencePanel, responseA, requestId, "A");
        }
        if (bufferB) {
            responseB += bufferB;
            void this.postStreamChunksToPanel(preferencePanel, responseB, requestIdB, "B");
        }
        return {
            responseA,
            responseB,
            workspaceFileChunksA,
            workspaceFileChunksB,
            preferencePanel,
        };
    }

    private onExternalSourceSearch = async (
        request: FindExternalSourcesRequest
    ): Promise<FindExternalSourcesResponse> => {
        // If the feature flag is not enabled for this version, return an empty list.
        if (
            !isExtensionVersionGte(
                this._featureFlagManager.currentFlags.vscodeExternalSourcesInChatMinVersion
            )
        ) {
            return {
                type: WebViewMessageType.findExternalSourcesResponse,
                data: { sources: [] },
            };
        }
        // If it is enabled for this version, call the API
        const data = await this._apiServer.searchExternalSources(request.data.query, []);
        return { type: WebViewMessageType.findExternalSourcesResponse, data };
    };

    private getActiveWorkspacePath = (): string | undefined => {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return undefined;
        }
        const activeFsPath = activeEditor.document.uri.fsPath;
        const activePath = this._workspaceManager.safeResolvePathName(activeFsPath);
        return activePath?.rootPath;
    };

    private _resolvePathName(uri: vscode.Uri): FileDetails | undefined {
        const qualifiedPathName = this._workspaceManager.safeResolvePathName(uri);
        if (qualifiedPathName === undefined) {
            return undefined;
        }
        return {
            repoRoot: qualifiedPathName.rootPath,
            pathName: qualifiedPathName.relPath,
        };
    }

    private _getDefaultTargetPath = (): FileDetails | undefined => {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return undefined;
        }

        const activeFsPath = activeEditor.document.uri.fsPath;
        const activePath = this._workspaceManager.safeResolvePathName(activeFsPath);

        return activePath ? pathNameToFileDetails(activePath) : undefined;
    };

    /**
     * Resolves a target path to a single file in the workspace.
     *
     * Resolution order:
     * 1. Absolute path: Directly resolved via workspace manager
     * 2. Relative path: Resolved relative to the best-match workspace root
     * 3. Fuzzy search: If the above methods fail, falls back to fuzzy file search
     *
     * @param targetFilePath - The path to resolve
     * @param searchScope - Optional search scope for fuzzy search
     * @returns FileDetails of the resolved file, or undefined if not found
     */
    public resolveTargetPath = async (
        targetFilePath: string | undefined,
        searchScope: ISearchScopeArgs | undefined = undefined,
        exactMatch: boolean = true
    ): Promise<FileDetails | undefined> => {
        if (!targetFilePath) {
            return undefined;
        }

        // Try to resolve the path via workspace manager
        // - Absolute paths are directly resolved
        // - Relative paths are resolved relative to the best-match workspace root
        // Resolved != exists. We still need to check for existence after
        let maybeResolvedPathName: IQualifiedPathName | undefined = undefined;
        if (isAbsolutePathName(targetFilePath)) {
            maybeResolvedPathName = this._workspaceManager.safeResolvePathName(targetFilePath);
        } else {
            // We attempt to find the best-match workspace root, then see if
            // that workspace root contains the target path (and actually exists)
            const bestMatch: IQualifiedPathInfo | undefined =
                this._workspaceManager.findBestWorkspaceRootMatch(targetFilePath);
            if (bestMatch) {
                maybeResolvedPathName = {
                    rootPath: bestMatch.qualifiedPathName.rootPath,
                    relPath: targetFilePath,
                };
            }
        }

        // We have resolved a file, but we need to double check that it actually exists
        if (maybeResolvedPathName) {
            const stat = await this._fuzzyFsSearcher.statPath(maybeResolvedPathName);
            if (stat) {
                const details = pathNameToFileDetails(maybeResolvedPathName);
                details.fileType = stat.type;
                return details;
            }
        }

        // If for some reason we cannot find a valid candidate path,
        // we fall back to the fuzzy fs searcher.
        // Resolve the target path with the fuzzy fs searcher
        const filePathResponse = this._fuzzyFsSearcher.findFiles({
            type: WebViewMessageType.findFileRequest,
            data: {
                rootPath: "",
                relPath: targetFilePath,
                exactMatch,
                maxResults: 10,
                searchScope,
            },
        });

        // If there is a file, return it
        if (filePathResponse.data.length >= 0) {
            return filePathResponse.data[0];
        }

        return undefined;
    };

    /**
     * Performs smart paste computation using the chat instruction stream.
     *
     * Returns a stream of "new code" that we can store until the user is ready to apply it.
     * Usually this is immediately upon pressing "Apply", but there are cases where we want
     * to pre-compute the stream and store the stream somewhere to reduce perceived latency.
     *
     * @param message
     * @param selectedCodeDetails
     * @returns: a stream of edits to apply to the file
     */
    private async *_smartPasteWithChatInstruction(
        request: ISmartPasteCacheContext,
        requestId?: string
    ): AsyncGenerator<ChatInstructionStreamResult> {
        const { message, targetFilePath, targetFileContent } = request;
        let selectedCodeDetails = request.selectedCodeDetails;

        // Get the contents
        requestId = requestId ?? this._apiServer.createRequestId();
        const blobs = this._workspaceManager.getContext().blobs;
        const chatHistory: Exchange[] = message.data.chatHistory.map((m) => ({
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: m.request_message,
            response_text: m.response_text,
            request_id: m.request_id,
            /* eslint-enable @typescript-eslint/naming-convention */
        }));

        // Get the last selected code in history
        let referenceRequestId: string | undefined = this._chatModel.getReferenceReqId();
        for (let i = chatHistory.length - 1; i >= 0; i--) {
            const request = chatHistory[i];
            const maybeSelectedCode = this._chatModel.selectionCache.get(request.request_id);
            if (maybeSelectedCode) {
                selectedCodeDetails = maybeSelectedCode;
                referenceRequestId = request.request_id;
                break;
            }
        }

        try {
            const smartPasteStream = await this._apiServer.smartPasteStream(
                requestId,
                "" /* instruction, should be empty */,
                blobs,
                chatHistory,
                selectedCodeDetails?.selectedCode,
                selectedCodeDetails?.prefix,
                selectedCodeDetails?.suffix,
                selectedCodeDetails?.path,
                undefined /* blobName */,
                selectedCodeDetails?.prefixBegin,
                selectedCodeDetails?.suffixEnd,
                selectedCodeDetails?.language,
                message.data.generatedCode,
                targetFilePath,
                targetFileContent,
                referenceRequestId
            );

            for await (const chunk of smartPasteStream) {
                yield chunk;
            }
        } catch (error) {
            this._logger.error(`Error in _smartPasteWithChatInstruction: ${getErrmsg(error)}`);
            throw error;
        }
    }

    /**
     * Handler for the clear metadata message.
     */
    private onClearMetadataFor = (msg: ChatClearMetadata): void => {
        const data: ChatClearMetadataData = msg.data;
        const chatModel: ChatModel = this._chatModel;

        // We remove the chat stream cache and the selection cache for the request ids
        // whenever the frontend requests they be deleted
        data.requestIds?.forEach((requestId: string) => {
            chatModel.deleteChatStream(requestId);
            chatModel.selectionCache.remove(requestId);

            // Clean up untruncated content associated with this request
            this.cleanupUntruncatedContentForRequest(requestId);
        });

        data.conversationIds?.forEach((conversationId: string) => {
            void this._checkpointManager.clearConversationCheckpoints(conversationId);

            // Clean up untruncated content associated with this conversation
            this.cleanupUntruncatedContentForConversation(conversationId);
        });

        // Clean up untruncated content associated with tool uses
        data.toolUseIds?.forEach((toolUseId: string) => {
            // Clean up untruncated content associated with this tool use
            this.cleanupUntruncatedContentForToolUse(toolUseId);
        });
    };

    /**
     * Cleans up untruncated content associated with a conversation
     *
     * @param conversationId The ID of the conversation to clean up
     */
    private cleanupUntruncatedContentForConversation = (conversationId: string): void => {
        this._cleanupUntruncatedContent(
            async (contentManager) =>
                await contentManager.cleanupConversationContent(conversationId),
            `conversation ${conversationId}`
        );
    };

    /**
     * Cleans up untruncated content associated with a request
     *
     * @param requestId The ID of the request to clean up
     */
    private cleanupUntruncatedContentForRequest = (requestId: string): void => {
        this._cleanupUntruncatedContent(
            async (contentManager) => await contentManager.cleanupRequestContent(requestId),
            `request ${requestId}`
        );
    };

    /**
     * Cleans up untruncated content associated with a tool use
     *
     * @param toolUseId The ID of the tool use to clean up
     */
    private cleanupUntruncatedContentForToolUse = (toolUseId: string): void => {
        this._cleanupUntruncatedContent(
            async (contentManager) => await contentManager.cleanupToolUseContent(toolUseId),
            `tool use ${toolUseId}`
        );
    };

    /**
     * Generic method to clean up untruncated content
     *
     * @param cleanupFn Function that performs the actual cleanup
     * @param logDescription Description to use in error logs
     */
    private _cleanupUntruncatedContent = (
        cleanupFn: (contentManager: UntruncatedContentManager) => Promise<number>,
        logDescription: string
    ): void => {
        // Get the SidecarToolHost to access the UntruncatedContentManager
        const sidecarToolHost = this._toolsModel.getSidecarToolHost();
        if (!sidecarToolHost) {
            return;
        }

        // Get the UntruncatedContentManager using the proper accessor method
        const contentManager = sidecarToolHost.getUntruncatedContentManager();
        if (!contentManager) {
            return;
        }

        // Call the cleanup method
        void cleanupFn(contentManager).catch((error: unknown) => {
            this._logger.error(
                `Failed to clean up untruncated content for ${logDescription}:`,
                error
            );
        });
    };

    private async _getVCSChange(): Promise<VCSChange> {
        if (this._workspaceManager !== undefined) {
            return await this._workspaceManager.getVCSChange();
        } else {
            return { commits: [], workingDirectory: [] };
        }
    }

    private onDidReceiveMessage = async (message: WebViewMessage): Promise<void> => {
        switch (message.type) {
            case WebViewMessageType.chatCreateFile: {
                const relPath = message.data.relPath;
                if (!relPath) {
                    try {
                        const document = await vscode.workspace.openTextDocument({
                            content: message.data.code,
                        });
                        const targetUri = document.uri;
                        await vscode.workspace.fs.writeFile(
                            targetUri,
                            Buffer.from(message.data.code)
                        );

                        await vscode.window.showTextDocument(document);
                    } catch (e: any) {
                        this._logger.error(`Could not create untitled file: ${e}`);
                    }
                    break;
                } else {
                    const bestWorkspaceRoot =
                        this._workspaceManager.findBestWorkspaceRootMatch(relPath);
                    if (!bestWorkspaceRoot || bestWorkspaceRoot.fileType !== FileType.directory) {
                        void vscode.window.showWarningMessage(
                            `Directory ${dirName(relPath)} not found.`
                        );
                        break;
                    }

                    // Get the URI of the file
                    const absPath = pathNameToAbsPath({
                        rootPath: bestWorkspaceRoot.qualifiedPathName.rootPath,
                        relPath: relPath,
                    });
                    const targetUri = vscode.Uri.file(absPath);

                    // Check if the file exists. If it does, raise an error
                    try {
                        await vscode.workspace.fs.stat(targetUri);
                        // If we find the file, we cannot continue
                        void vscode.window.showErrorMessage(
                            `Cannot create file at ${targetUri.fsPath}. File already exists.`
                        );
                        break;
                    } catch {
                        // If we find the file does not exist, we can continue
                    }

                    // Create the file, open it, and insert the code
                    try {
                        // Create intermediate directories
                        await vscode.workspace.fs.writeFile(
                            targetUri,
                            Buffer.from(message.data.code)
                        );
                        // openTextDocument OK: displaying it in the window
                        const maybeDoc = await vscode.workspace.openTextDocument(targetUri);
                        if (!maybeDoc) {
                            this._logger.error(
                                `Could not open document at ${targetUri.fsPath} during file creation.`
                            );
                            break;
                        }
                        await vscode.window.showTextDocument(maybeDoc);
                    } catch (e: any) {
                        this._logger.error(`Could not create file at ${targetUri.fsPath}: ${e}`);
                    }
                }
                break;
            }
            case WebViewMessageType.chatSmartPaste: {
                const activeEditor = vscode.window.activeTextEditor;
                let selectedCodeDetails: SelectedCodeDetails | null = null;
                if (activeEditor) {
                    selectedCodeDetails = getSelectedCodeDetails(
                        activeEditor,
                        this._workspaceManager,
                        MAX_PREFIX_SUFFIX_CHARS,
                        MAX_PREFIX_SUFFIX_CHARS
                    );
                }

                let targetFilePath: string;
                const maybeFile =
                    (await this.resolveTargetPath(message.data.targetFile)) ??
                    this._getDefaultTargetPath();

                // On a non-precomputation, we may want to show some UI
                if (!message.data.options?.dryRun) {
                    // Show a warning if we don't have a file
                    if (!maybeFile) {
                        void vscode.window.showWarningMessage(
                            "Cannot apply codeblock. No valid target file found."
                        );
                        this._clientMetricsReporter.reportWebviewClientMetric({
                            webviewName: WebviewName.chat,
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            client_metric: ChatMetricName.chatFailedSmartPasteResolveFile,
                            value: 1,
                        });
                        return;
                        // Pop up the confirmation modal if we require confirmation
                    } else if (message.data.options?.requireFileConfirmation) {
                        const response = await this.onOpenConfirmationModal({
                            type: WebViewMessageType.openConfirmationModal,
                            data: {
                                title: "Apply Codeblock",
                                message: `Are you sure you want to apply the codeblock to ${maybeFile.pathName}?`,
                                confirmButtonText: "Apply",
                                cancelButtonText: "Cancel",
                            },
                        });
                        if (!response.data.ok) {
                            return;
                        }
                    }
                }

                // If we do not have a smart paste target, we cannot do smart paste
                if (!maybeFile) {
                    return;
                }

                targetFilePath = pathNameToAbsPath({
                    rootPath: maybeFile.repoRoot,
                    relPath: maybeFile.pathName,
                });
                const targetFileContent = (await viewTextDocument(targetFilePath)).getText();
                const targetFileQualifiedPath: QualifiedPathName = QualifiedPathName.from({
                    rootPath: maybeFile.repoRoot,
                    relPath: maybeFile.pathName,
                });

                let applyTime;
                if (!message.data.options?.dryRun && !message.data.options?.instantApply) {
                    this._createOrShowDiffView({
                        smartPasteContext: { applyTime },
                        document: new VSCodeDiffViewDocument(
                            targetFileQualifiedPath,
                            targetFileContent,
                            message.data.generatedCode
                        ),
                    });
                }

                // Maybe start the instruction stream
                let editInstructionStream: AsyncGenerator<ChatInstructionStreamResult>;
                let requestId: string;
                if (this._config.config.chat.smartPasteUsePrecomputation ?? true) {
                    const result = await this._smartPasteCache.get(
                        message.data.generatedCode,
                        targetFilePath,
                        targetFileContent,
                        {
                            message,
                            selectedCodeDetails,
                            targetFilePath,
                            targetFileContent,
                        }
                    );
                    if (!result) {
                        this._logger.error(`Could not get valid edit stream for smart paste.`);
                        return;
                    }
                    editInstructionStream = result.generator;
                    requestId = result.requestId;
                    this._clientMetricsReporter.reportWebviewClientMetric({
                        webviewName: WebviewName.chat,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: ChatMetricName.chatPrecomputeSmartPaste,
                        value: 1,
                    });
                } else if (!message.data.options?.dryRun) {
                    const result = await this._smartPasteCache.getDirect({
                        message,
                        selectedCodeDetails,
                        targetFilePath,
                        targetFileContent,
                    });
                    if (!result) {
                        this._logger.error(`Could not get valid edit stream for smart paste.`);
                        return;
                    }
                    editInstructionStream = result.generator;
                    requestId = result.requestId;
                    this._clientMetricsReporter.reportWebviewClientMetric({
                        webviewName: WebviewName.chat,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: ChatMetricName.chatSmartPaste,
                        value: 1,
                    });
                } else {
                    return;
                }

                if (!editInstructionStream) {
                    this._logger.error(`Could not get valid edit stream for smart paste.`);
                    return;
                }

                // Only actually show the diff view if we are not in dry run mode
                if (!message.data.options?.dryRun) {
                    if (message.data.options?.instantApply) {
                        await DiffViewWebviewPanel.instantApply(
                            {
                                extensionUri: this._extensionUri,
                                workspaceManager: this._workspaceManager,
                                apiServer: this._apiServer,
                                keybindingWatcher: this._keybindingWatcher,
                                fuzzyFsSearcher: this._fuzzyFsSearcher,
                                fuzzySymbolSearcher: this._fuzzySymbolSearcher,
                                workTimer: this._workTimer,
                            },
                            {
                                document: new VSCodeDiffViewDocument(
                                    targetFileQualifiedPath,
                                    targetFileContent,
                                    message.data.generatedCode
                                ),
                            },
                            editInstructionStream
                        );
                    } else {
                        DiffViewWebviewPanel.startStream(
                            toEditStream(targetFileContent, editInstructionStream),
                            requestId,
                            SessionOrigin.smartPaste
                        );
                    }
                }

                break;
            }
            case WebViewMessageType.openFile: {
                // Resolve the best filepath we can
                const resolvedFile: FileDetails | undefined = await this.resolveTargetPath(
                    message.data.pathName,
                    undefined,
                    false
                );
                if (!resolvedFile && message.data.allowOutOfWorkspace) {
                    // If we allow out of workspace, we can just open the file directly
                    void openFileFromMessage(message.data);
                    return;
                } else if (!resolvedFile) {
                    return;
                }

                const repoRoot = resolvedFile.repoRoot;
                const pathName = resolvedFile.pathName;

                void openFileFromMessage({
                    repoRoot,
                    pathName,
                    range: message.data.range,
                    fullRange: message.data.fullRange,
                    differentTab: message.data.differentTab,
                    snippet: message.data.snippet,
                });
                break;
            }
            case WebViewMessageType.showNotification: {
                if (message.data.type === "error") {
                    await vscode.window.showErrorMessage(message.data.message);
                    return;
                } else if (message.data.type === "warning") {
                    await vscode.window.showWarningMessage(message.data.message);
                    return;
                } else {
                    await vscode.window.showInformationMessage(message.data.message);
                }
                break;
            }
            case WebViewMessageType.openDiffInBuffer: {
                const { oldContents, newContents, filePath } = message.data;
                try {
                    await openDiffInBuffer(oldContents, newContents, filePath);
                } catch (error) {
                    this._logger.error("Failed to open diff in buffer:", error);
                }
                break;
            }
            case WebViewMessageType.openMemoriesFile: {
                const memoriesPath = this._toolsModel.memoriesAbsPath;
                const memoriesTextEditorEnabled =
                    this._featureFlagManager.currentFlags.memoriesTextEditorEnabled;
                if (!memoriesPath) {
                    return;
                }
                void openFileFromMessage({
                    repoRoot: "",
                    pathName: memoriesPath,
                    // Open in the custom memories text editor if the feature flag is enabled
                    openTextDocument: memoriesTextEditorEnabled ? undefined : true,
                });
                break;
            }
            case WebViewMessageType.mainPanelPerformAction: {
                void this.performAction(message.data);
                break;
            }
            case WebViewMessageType.usedSlashAction: {
                this._onboardingSessionEventReporter.reportEvent(
                    OnboardingSessionEventName.UsedSlashAction
                );
                break;
            }
            case WebViewMessageType.usedChat: {
                this._onboardingSessionEventReporter.reportEvent(
                    OnboardingSessionEventName.UsedChat
                );
                break;
            }
            case WebViewMessageType.chatClearMetadata: {
                this.onClearMetadataFor(message);
                break;
            }
            case WebViewMessageType.openGuidelines: {
                this._openGuidelines(message.data);
                break;
            }
            case WebViewMessageType.updateUserGuidelines: {
                GuidelinesWatcher.updateUserGuidelines(message.data);
                break;
            }
            case WebViewMessageType.openSettingsPage: {
                // If a section is specified, pass it as a parameter to the command
                if (message.data) {
                    void vscode.commands.executeCommand(
                        "vscode-augment.showSettingsPanel",
                        message.data
                    );
                } else {
                    void vscode.commands.executeCommand("vscode-augment.showSettingsPanel");
                }
                break;
            }
            case WebViewMessageType.augmentLink: {
                // Open external URL in browser
                try {
                    const url = message.data;
                    if (url) {
                        const uri = vscode.Uri.parse(url);
                        void vscode.env.openExternal(uri);
                    }
                } catch (e) {
                    this._logger.error(`Failed to open URL: ${getErrmsg(e)}`);
                }
                break;
            }
            case WebViewMessageType.triggerInitialOrientation: {
                if (
                    !this._featureFlagManager.currentFlags.memoriesParams
                        ?.enable_initial_orientation
                ) {
                    return;
                }

                await runInitialOrientationWithProgressSingleton(
                    this._apiServer,
                    this._workspaceManager,
                    this._featureFlagManager,
                    this._checkpointManager,
                    InitialOrientationCaller.onboarding
                );
                break;
            }
            case WebViewMessageType.getOrientationStatus: {
                sendOrientationStatusToWebviews();
                break;
            }
            case WebViewMessageType.executeCommand: {
                const commandId = message.data;
                try {
                    await vscode.commands.executeCommand(commandId);
                } catch (e: any) {
                    this._logger.error(`Failed to execute command ${commandId}: ${e}`);
                }
                break;
            }
            case WebViewMessageType.toggleCollapseUnchangedRegions: {
                try {
                    await vscode.commands.executeCommand(
                        "diffEditor.toggleCollapseUnchangedRegions"
                    );
                } catch (e: any) {
                    this._logger.error(
                        `Failed to execute diffEditor.toggleCollapseUnchangedRegions: ${e}`
                    );
                }
                break;
            }
            case WebViewMessageType.dismissBannerNotification: {
                if (this._notificationWatcher) {
                    await this._notificationWatcher.dismissBannerNotification(
                        message.data.notificationId,
                        message.data.actionItemTitle
                    );
                }
                break;
            }
            case WebViewMessageType.reportError: {
                void this._apiServer.reportError(
                    message.data.originalRequestId,
                    message.data.sanitizedMessage,
                    message.data.stackTrace,
                    message.data.diagnostics
                );
                this._logger.error(
                    `reportError: ${message.data.sanitizedMessage} ${message.data.stackTrace} ${JSON.stringify(
                        message.data.diagnostics
                    )}`
                );
                break;
            }
        }
    };

    private _openGuidelines = (data: string): void => {
        // Open the user guidelines if the file path is empty
        if (!data) {
            void vscode.commands.executeCommand(
                "workbench.action.openSettings",
                "augment.userGuidelines"
            );
            return;
        } else {
            // Open the workspace guidelines if the file path is not empty
            const filePath = path.join(data, AUGMENT_GUIDELINES_FILE);
            const uri = vscode.Uri.file(filePath);
            void vscode.commands.executeCommand("vscode.open", uri);
            return;
        }
    };

    private _getBlobNames(files: IQualifiedPathName[]): string[] {
        const blobNames = new Array<string>();
        for (const qualifiedPathName of files) {
            const blobName = this._workspaceManager.getBlobName(
                QualifiedPathName.from(qualifiedPathName)
            );
            if (blobName !== undefined) {
                blobNames.push(blobName);
            }
        }
        return blobNames;
    }

    private performAction(action: string): void {
        switch (action) {
            case "disable-github-copilot": {
                void ConflictingExtensions.disableGitHubCopilot();
                break;
            }
            case "disable-codeium": {
                void ConflictingExtensions.disableCodeium();
                break;
            }
            case "move-extension-aside": {
                this._actionsModel.setSystemStateStatus(
                    SystemStateName.hasMovedExtensionAside,
                    SystemStatus.complete
                );
                break;
            }
            case "open-folder": {
                void vscode.commands.executeCommand("vscode.openFolder");
                break;
            }
            case "close-folder": {
                void vscode.commands.executeCommand("vscode.closeFolder");
                break;
            }
            case "grant-sync-permission": {
                this._syncingEnabledTracker.enableSyncing();
                break;
            }
        }
    }

    private handleDerivedStateChange(derivedStates: DerivedState[]) {
        void this.sendActionsToWebview(derivedStates.map((s) => s.name));
    }

    private async sendActionsToWebview(actions: Array<DerivedStateName>) {
        await this._webview?.postMessage({
            type: WebViewMessageType.mainPanelActions,
            data: actions,
        });
    }

    public onChatExtensionMessage = async (message: ChatExtensionMessage): Promise<void> => {
        if (typeof message === "string") {
            switch (message) {
                case chatExtensionMessage.runSlashFix: {
                    await this._webview?.postMessage({
                        type: WebViewMessageType.runSlashCommand,
                        data: "fix",
                    });
                    break;
                }
                case chatExtensionMessage.runSlashExplain: {
                    await this._webview?.postMessage({
                        type: WebViewMessageType.runSlashCommand,
                        data: "explain",
                    });
                    break;
                }
                case chatExtensionMessage.newThread: {
                    await this._webview?.postMessage({
                        type: WebViewMessageType.newThread,
                    });
                    break;
                }
                case chatExtensionMessage.runSlashTest: {
                    await this._webview?.postMessage({
                        type: WebViewMessageType.runSlashCommand,
                        data: "write-test",
                    });
                    break;
                }
                case chatExtensionMessage.runSlashDocument: {
                    await this._webview?.postMessage({
                        type: WebViewMessageType.runSlashCommand,
                        data: "document",
                    });
                    break;
                }
                case chatExtensionMessage.resetAgentOnboarding: {
                    await this._webview?.postMessage({
                        type: WebViewMessageType.resetAgentOnboarding,
                    });
                    break;
                }
            }
        } else if (message.type === chatExtensionMessage.newThread) {
            await this._webview?.postMessage({
                type: WebViewMessageType.newThread,
                data: {
                    mode: message.mode,
                },
            });
        }
    };

    private showAugmentPanel = async (): Promise<EmptyMessage> => {
        await vscode.commands.executeCommand(FocusAugmentPanel.commandID);
        return { type: WebViewMessageType.empty };
    };

    private callTool = async (message: CallToolMessage): Promise<CallToolResponse> => {
        const toolCall = message.data;
        const hydratedHistory = await this._chatModel.hydrateChatHistory(toolCall.chatHistory);
        const limitedHistory = this._chatModel.limitChatHistory(hydratedHistory);
        const toolUseResult = await this._toolsModel.callTool(
            toolCall.chatRequestId,
            toolCall.toolUseId,
            toolCall.name,
            toolCall.input,
            limitedHistory,
            toolCall.conversationId
        );
        return {
            type: WebViewMessageType.callToolResponse,
            data: toolUseResult,
        };
    };

    private cancelToolRun = async (
        message: CancelToolRunMessage
    ): Promise<CancelToolRunResponse> => {
        await this._toolsModel.cancelToolRun(message.data.requestId, message.data.toolUseId);
        return {
            type: WebViewMessageType.cancelToolRunResponse,
        };
    };

    // TODO(AU-8180): Remove this function once it is no longer referenced.
    /**
     * Checks if a tool exists.
     *
     * @param message - The check tool exists request message
     * @returns A promise that resolves to a CheckToolExistsResponse containing whether the tool exists
     */
    private checkToolExists = async (
        message: CheckToolExistsRequest
    ): Promise<CheckToolExistsResponse> => {
        const exists = await this._toolsModel.checkToolExists(message.toolName);
        return {
            type: WebViewMessageType.checkToolExistsResponse,
            exists,
        };
    };

    private _getChatRequestIdeState = async (
        _message: GetChatRequestIdeStateRequest
    ): Promise<GetChatRequestIdeStateResponse> => {
        // We'll order the workspace folders by what's currently open and what's most
        // recently changed.
        const currentlyOpenFolderRoot = vscode.window.activeTextEditor
            ? this._workspaceManager.getFolderRoot(vscode.window.activeTextEditor?.document.uri)
            : undefined;
        const mostRecentlyChangedFolderRoot =
            this._workspaceManager.getMostRecentlyChangedFolderRoot();
        const workspaceFolders = this._workspaceManager
            .listSourceFolders()
            .filter((f) => f.type === SourceFolderType.vscodeWorkspaceFolder)
            .sort((f) => {
                if (f.folderRoot === currentlyOpenFolderRoot) {
                    return -1;
                } else if (f.folderRoot === mostRecentlyChangedFolderRoot) {
                    return 0;
                } else {
                    return 1;
                }
            })
            .map((f) => ({
                /* eslint-disable @typescript-eslint/naming-convention */
                folder_root: f.folderRoot,
                repository_root:
                    this._workspaceManager.getRepoRootForFolderRoot(f.folderRoot) ?? f.folderRoot,
                /* eslint-enable @typescript-eslint/naming-convention */
            }));

        const launchProcessTool = this._toolsModel.getTool(LocalToolType.launchProcess);
        let terminalInfo: TerminalInfo | undefined;
        if (launchProcessTool && launchProcessTool instanceof TerminalLaunchProcessTool) {
            terminalInfo = launchProcessTool.processTools.getLongRunningTerminalInfo();
        }
        if (!terminalInfo && workspaceFolders.length > 0) {
            // If there was no long running terminal, then we haven't opened one yet,
            // and it will open at the folder root.
            terminalInfo = {
                /* eslint-disable @typescript-eslint/naming-convention */
                terminal_id: 0,
                current_working_directory: workspaceFolders[0].folder_root,
                /* eslint-enable @typescript-eslint/naming-convention */
            };
        }

        return Promise.resolve({
            type: WebViewMessageType.getChatRequestIdeStateResponse,
            /* eslint-disable @typescript-eslint/naming-convention */
            data: {
                workspace_folders: workspaceFolders,
                workspace_folders_unchanged: false,
                current_terminal: terminalInfo,
            },
            /* eslint-enable @typescript-eslint/naming-convention */
        });
    };

    // A utility function to create or show the diff view panel using
    // the dependencies tracked by this class
    private _createOrShowDiffView = (options: DiffViewPanelOptions): void => {
        DiffViewWebviewPanel.createOrShow(
            {
                extensionUri: this._extensionUri,
                workspaceManager: this._workspaceManager,
                apiServer: this._apiServer,
                keybindingWatcher: this._keybindingWatcher,
                fuzzyFsSearcher: this._fuzzyFsSearcher,
                fuzzySymbolSearcher: this._fuzzySymbolSearcher,
                workTimer: this._workTimer,
            },
            options
        );
    };

    private _notifyAgentEditListHasUpdates = throttle(
        () => {
            void this._webview?.postMessage({
                type: WebViewMessageType.chatAgentEditListHasUpdates,
                data: {},
            });
        },
        1000 /* 1 second */,
        { leading: true, trailing: true }
    );

    /**
     * Notifies the webview that agent memories have been updated.
     * This is used to trigger animations in the UI.
     */
    private _notifyMemoryHasUpdates = throttle(
        () => {
            void this._webview?.postMessage({
                type: WebViewMessageType.chatMemoryHasUpdates,
            });
        },
        500 /* 0.5 seconds */,
        { leading: true, trailing: true }
    );

    /**
     * Sets up a listener for memory updates from the SidecarToolHost's MemoryUpdateManager.
     * This is used to trigger animations in the UI when memories are updated.
     */
    private setupMemoryUpdateListener(): void {
        // Check if the ToolsModel has the getSidecarToolHost method
        if (!this._toolsModel || typeof this._toolsModel.getSidecarToolHost !== "function") {
            return;
        }

        // Get the SidecarToolHost from the ToolsModel
        const sidecarToolHost = this._toolsModel.getSidecarToolHost();
        if (!sidecarToolHost) {
            return;
        }

        // Get the MemoryUpdateManager from the SidecarToolHost
        const memoryUpdateManager = sidecarToolHost.getMemoryUpdateManager();
        if (!memoryUpdateManager) {
            return;
        }

        // Clean up any existing subscription
        this._memoryUpdateManagerDisposable?.dispose();

        // Register for memory update notifications
        this._memoryUpdateManagerDisposable = memoryUpdateManager.onMemoryHasUpdates(() => {
            this._notifyMemoryHasUpdates();
        });

        // Add the disposable to our collection
        this.addDisposable(this._memoryUpdateManagerDisposable);
    }

    private _saveImage = async (request: ChatSaveImageRequest): Promise<ChatSaveImageResponse> => {
        const { filename, data } = request.data;
        // data is now a base64-encoded string, so we can save it directly
        const bytes: Uint8Array = base64ToBytes(data);
        await this._assetManager.saveAsset(filename, bytes);
        return {
            type: WebViewMessageType.chatSaveImageResponse,
            data: filename,
        };
    };

    private _saveAttachment = async (
        request: ChatSaveAttachmentRequest
    ): Promise<ChatSaveAttachmentResponse> => {
        const { filename, data } = request.data;
        // data is now a base64-encoded string, so we can save it directly
        const bytes: Uint8Array = base64ToBytes(data);
        await this._assetManager.saveAsset(filename, bytes);
        return {
            type: WebViewMessageType.chatSaveAttachmentResponse,
            data: filename,
        };
    };

    private _deleteImage = async (
        request: ChatDeleteImageRequest
    ): Promise<ChatDeleteImageResponse> => {
        const filename = request.data;
        await this._assetManager.deleteAsset(filename);
        return {
            type: WebViewMessageType.chatDeleteImageResponse,
        };
    };

    /**
     * Loads an image from the asset manager.
     *
     * @param request - The load image request message
     * @returns The base64-encoded image data or undefined if not found
     */
    private _loadImage = async (request: ChatLoadImageRequest): Promise<ChatLoadImageResponse> => {
        const filename = request.data;
        const bytes = await this._assetManager.loadAsset(filename);
        // convert to base64-encoded string
        const data: string | undefined = bytes ? bytesToBase64(bytes) : undefined;
        return {
            type: WebViewMessageType.chatLoadImageResponse,
            data,
        };
    };

    private async getSubscriptionInfo(
        _: GetSubscriptionInfoRequest
    ): Promise<GetSubscriptionInfoResponse> {
        try {
            const subscriptionInfo = await this._apiServer.getSubscriptionInfo();

            // Check if we have subscription data
            if (!subscriptionInfo) {
                return {
                    type: WebViewMessageType.getSubscriptionInfoResponse,
                    data: {},
                };
            }

            const subscription = subscriptionInfo.subscription;
            const retVal: GetSubscriptionInfoResponse = {
                type: WebViewMessageType.getSubscriptionInfoResponse,
                data: {
                    enterprise: subscription?.Enterprise ? {} : undefined,
                    activeSubscription: subscription?.ActiveSubscription
                        ? {
                              endDate: subscription.ActiveSubscription.end_date ?? undefined,
                              usageBalanceDepleted:
                                  subscription.ActiveSubscription.usage_balance_depleted,
                          }
                        : undefined,
                    inactiveSubscription: subscription?.InactiveSubscription ? {} : undefined,
                },
            };
            return retVal;
        } catch (error) {
            this._logger.error(`Failed to get subscription info: ${String(error)}`);
            throw error;
        }
    }
}

// TEMPORARY HELLO WORLD FOR DOGFOOD FOR PROTOBUF BOOTSTRAPPING
function registerProtoServices(webview: vscode.Webview): SendMessageTransport {
    const transport: SendMessageTransport = new SendMessageTransport({
        sendMessage: (msg: unknown) => {
            void webview.postMessage(msg);
        },
        onReceiveMessage: (callback: (message: unknown) => void) => {
            const disposable = webview.onDidReceiveMessage(callback);
            return () => {
                disposable?.dispose();
            };
        },
    });
    const registry: ServiceRegistry = ServiceRegistry.withGrpcServiceTransport(transport);
    registry.registerService(TestService, {
        testMethod: (_input): TestResponse => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
            return create(TestResponseSchema, { result: "Hello World!" });
        },
        errorMethod: (): Promise<TestResponse> => {
            return Promise.reject(new Error("Intentional error from service"));
        },
    });
    return transport;
}
