/* eslint-disable @typescript-eslint/naming-convention */
import { FeatureVector } from "@augment-internal/feature-vector-collector";
import {
    SidecarAPIServer,
    SidecarAPIServerImpl,
} from "@augment-internal/sidecar-libs/src/api/augment-api";
import {
    BackChatResult,
    Blobs,
    FetchFunction,
    InvalidCompletionURLError,
    ModelInfoRegistryEntry,
} from "@augment-internal/sidecar-libs/src/api/types";
import {
    blobsToBlobsPayload,
    safeJsonStringify,
    toChatResult,
    toVCSChangePayload,
} from "@augment-internal/sidecar-libs/src/api/utils";
import {
    BlobsPayload,
    ChatMode,
    ChatPayload,
    ChatRequestNode,
    ChatResultNode,
    Exchange,
    PersonaType,
    ReplacementText,
    VCSChangePayload,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ExperimentTreatment } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import {
    AgentCodebaseRetrievalOptions,
    AgentCodebaseRetrievalResult,
    AgentRequestEvent,
    AgentSessionEvent,
    ChatResult,
    RemoteAgentSessionEvent,
    ToolUseRequestEvent,
    ToolUseRequestEventPayload,
} from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { APIError, getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
// Used for to translate API types into internal types.
import {
    CreateRemoteAgentRequest,
    CreateRemoteAgentResponse,
    GetRemoteAgentChatHistoryResponse,
    GithubRepo,
    IsUserGithubConfiguredResponse,
    ListGithubRepoBranchesRequest,
    ListGithubRepoBranchesResponse,
    ListGithubReposForAuthenticatedUserRequest,
    ListGithubReposForAuthenticatedUserResponse,
    ListRemoteAgentsResponse,
    RemoteAgent,
    RemoteAgentAddSSHKeyRequest,
    RemoteAgentAddSSHKeyResponse,
    RemoteAgentChatRequestDetails,
    RemoteAgentStatus,
    RemoteAgentWorkspaceLogsRequest,
    RemoteAgentWorkspaceLogsResponse,
    RemoteAgentWorkspaceSetup,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import {
    RemoteToolDefinition,
    RemoteToolResponseStatus,
    RunRemoteToolResult,
} from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
    AtlassianToolExtraInput,
    ExtraToolInput,
    GitHubToolExtraInput,
    LinearToolExtraInput,
    NotionToolExtraInput,
    RemoteToolId,
    ToolAvailabilityStatus,
    ToolDefinition,
    ToolSafety,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { abortSignalAny } from "@augment-internal/sidecar-libs/src/utils/abort-signal";
import {
    toBoolean,
    toNumber,
    toString,
    toStringArray,
    verifyArray,
} from "@augment-internal/sidecar-libs/src/utils/json-utils";
import { getPropertySizes } from "@augment-internal/sidecar-libs/src/utils/object-utils";
import {
    retryWithBackoff,
    withTimeout,
} from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { v4 as uuidv4 } from "uuid";

import { APITiming } from "./api/api-timing";
import { AugmentConfig, AugmentConfigListener } from "./augment-config-listener";
import { AuthSessionStore } from "./auth/auth-session-store";
import { ChatFeedback } from "./chat/chat-types";
import { CompletionFeedback } from "./completions/completion-types";
import { defaultFeatureFlags, FeatureFlags } from "./feature-flags";
import { defaultSupportedLanguages } from "./languages";
import { type AugmentLogger, getLogger } from "./logging";
import { ClientMetric as TClientMetric } from "./metrics/types";
import { FileEditEvent, SingleEdit } from "./next-edit/file-edit-events";
import {
    BlockedPathAndRange,
    DiffSpan,
    NextEditFeedback,
    NextEditMode,
    NextEditResult,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
} from "./next-edit/next-edit-types";
import { NotificationDisplayType } from "./notification-watcher";
import { GetNotificationsResponse, NotificationLevel } from "./notification-watcher";
import { OnboardingSessionEventName } from "./onboarding/onboarding-types";
import { ChangedFileStats, CommitMessagePromptData, KeyValuePair } from "./utils/types";
import { utf32Length } from "./utils/unicode";
import { LIFOWorkQueue } from "./utils/work-queue";
import { EloModelConfiguration } from "./webview-panels/preference-panel-types";
import {
    RemoteAgentResumeHintReason,
    type SearchExternalSourcesResponse,
    SmartPastePrecomputeMode,
    UserTier,
} from "./webview-providers/webview-messages";
import { ViewedContentInfo } from "./workspace/viewed-content-tracker";

export {
    GetNotificationsResponse,
    NextEditMode,
    NextEditScope,
    NotificationLevel,
    SearchExternalSourcesResponse,
};

// CompletionLocation is a set of character offsets into a blob that is the target
// of a completion request.
//  - prefixBegin: offset of the beginning of the completion's prefix
//  - cursorPosition: offset of the cursor position
//  - suffixEnd: offset of the first character after the completion's suffix
export type CompletionLocation = {
    prefixBegin: number;
    cursorPosition: number;
    suffixEnd: number;
};

export type LastCompletionDetails = {
    // The prefix used for the last completion
    prefix: string;

    // The generated suggestion for the last completion
    suggestion: string;

    // The time the completion was issued or reused
    time: number;

    // What was the length of the completion the last
    // time it was reused (this number becomes smaller
    // as the user types more of the completion).
    reusedCompletionLength: number;
};

/**
 * CompletionItem represent one single completion.
 *
 * Each contains all necessary data to construct an vscode.InlineCompletionItem
 */
export interface CompletionItem {
    text: string;
    skippedSuffix: string;
    suffixReplacementText: string;
    filterScore?: number;
}

/**
 * CompletionResult defines a response from a completion request.
 *
 * This contains of an array of CompletionItem each converted to
 * one completion item in vscode, as well as other data associated
 * with the completion that are not directly associated with completions.
 *
 * This is represents the internal interface used inside the extension and
 * is converted from a BackCompletionResult which conforms to the interface
 * of the actual API call.
 */
export type CompletionResult = {
    completionItems: CompletionItem[];
    unknownBlobNames: string[];
    checkpointNotFound: boolean;
    suggestedPrefixCharCount?: number;
    suggestedSuffixCharCount?: number;
    completionTimeoutMs?: number;
};

export type CheckpointBlobsResult = {
    newCheckpointId: string;
};

export type MemorizeResult = {
    blobName: string;
};

export type BatchUploadResult = {
    blobNames: string[];
};

export type FindMissingResult = {
    unknownBlobNames: string[];
    nonindexedBlobNames: string[];
};

export type CodeEditResult = {
    modifiedCode: string;
    unknownBlobNames: string[];
    checkpointNotFound: boolean;
};

export type ChatInstructionStreamResult = {
    text: string;
    unknownBlobNames?: string[];
    checkpointNotFound?: boolean;
    replacementText?: string;
    replacementOldText?: string;
    replacementStartLine?: number;
    replacementEndLine?: number;
    replacementSequenceId?: number;
};

export type GenerateCommitMessageResult = {
    text: string;
};

export type LineRange = {
    start: number;
    stop: number;
};

export type FileLocation = {
    path: string;
    range: LineRange;
    header?: string;
};

export type ScoredFileHunk = {
    fileLocation: FileLocation;
    originalCode: string;
    updatedCode: string;
    truncationChar?: number;
    localizationScore: number;
    editingScore: number;
    changeDescription: string;
    diffSpans?: DiffSpan[];
};

export type Location = {
    path: string;
    range: LineRange;
};

export type Scored = {
    item: Location;
    score: number;
    debugInfo?: string;
};

export type NextEditLocationResult = {
    candidateLocations: Scored[];
    unknownBlobNames: string[];
    checkpointNotFound: boolean;
    criticalErrors: string[];
};

export type DiagnosticFileLocation = {
    path: string;
    line_start: number;
    line_end: number;
};

export enum DiagnosticSeverity {
    /* These names must match the backend ProtoBuf */
    Error = "ERROR",
    Warning = "WARNING",
    Information = "INFORMATION",
    Hint = "HINT",
}

export type Diagnostic = {
    message: string;
    severity: DiagnosticSeverity;
    current_blob_name: string;
    location: DiagnosticFileLocation;
    blob_name: string; // indexed blob name
    char_start: number;
    char_end: number;
};

export type NextEditGenerationResult = {
    result: NextEditResult;
    unknownBlobNames: string[];
    checkpointNotFound: boolean;
};

/** Describes request to nextEditStream() using client types. */
export type NextEditStreamRequest = {
    requestId: string;
    mode: NextEditMode;
    scope: NextEditScope;
    blobs?: Blobs;
    blobName?: string;
    recentChanges?: ReplacementText[];
    fileEditEvents?: FileEditEvent[];
    instruction?: string;
    selectedCode?: string;
    prefix?: string;
    suffix?: string;
    pathName?: IQualifiedPathName;
    language?: string;
    blockedLocations?: BlockedPathAndRange[];
    diagnostics?: Diagnostic[];
    clientCreatedAt: Date;
    unindexedEditEvents: FileEditEvent[];
    unindexedEditEventsBaseBlobNames: string[];
};

export type SaveChatResult = {
    uuid: string;
    url: string;
};

export type GetSubscriptionInfoResponse = {
    subscription: {
        ActiveSubscription?: {
            end_date?: string;
            usage_balance_depleted: boolean;
        };
        Enterprise?: {};
        InactiveSubscription?: {};
    };
};

export type BackAgentCodebaseRetrievalResult = {
    formatted_retrieval: string;
};

export type BackAgentEditFileResult = {
    modified_file_contents: string;
    is_error: boolean;
};

export type AgentEditFileResult = {
    modifiedFileContents: string;
    isError: boolean;
};

export enum BackToolSafety {
    // Tool always needs user approval to run.
    Unsafe = 0,
    // Tool does not need user approval to run.
    Safe = 1,
    // For some inputs, the tool needs user approval and for some it does not.
    Check = 2,
}

export type BackListRemoteToolsResponseMessage = {
    tool_definition: ToolDefinition;
    remote_tool_id: RemoteToolId;
    availability_status: ToolAvailabilityStatus;
    tool_safety: BackToolSafety;
    oauth_url: string;
};

export type BackListRemoteToolsResult = {
    tools: BackListRemoteToolsResponseMessage[];
};

export type ListRemoteToolsResult = {
    tools: RemoteToolDefinition[];
};

export type BackCheckToolSafetyResult = {
    is_safe: boolean;
};

export type BackRunRemoteToolResult = {
    tool_output: string;
    tool_result_message: string;
    status: RemoteToolResponseStatus;
};

export enum RevokeToolAccessStatus {
    Unknown = 0,
    Unimplemented = 1,
    NotFound = 2,
    Success = 3,
    NotActive = 4,
    Failed = 5,
}

export type BackRevokeToolAccessResult = {
    status: RevokeToolAccessStatus;
};

export type RevokeToolAccessResult = {
    status: RevokeToolAccessStatus;
};

/**
 * Backend notification action item type
 * Maps to the ActionItem message in public_api.proto
 */
export type BackNotificationActionItem = {
    title: string;
    url: string;
};

/**
 * Backend notification level type
 * Maps to the NotificationLevel message in public_api.proto
 */
export enum BackNotificationLevel {
    NOTIFICATION_LEVEL_UNSPECIFIED = 0,
    NOTIFICATION_LEVEL_INFO = 1,
    NOTIFICATION_LEVEL_WARNING = 2,
    NOTIFICATION_LEVEL_ERROR = 3,
}

/**
 * Backend notification display type
 * Maps to the NotificationDisplayType message in public_api.proto
 */
export enum BackNotificationDisplayType {
    DISPLAY_TYPE_UNSPECIFIED = 0,
    DISPLAY_TYPE_TOAST = 1,
    DISPLAY_TYPE_BANNER = 2,
}
/**
 * Backend notification type
 * Maps to the Notification message in public_api.proto
 */
export type BackNotification = {
    notification_id: string;
    level: BackNotificationLevel;
    message: string;
    action_items?: BackNotificationActionItem[];
    display_type: BackNotificationDisplayType;
};

/**
 * Backend response for reading notifications
 * Maps to the ReadNotificationsResponse message in public_api.proto
 */
export type BackReadNotificationsResponse = {
    notifications: BackNotification[];
};

export type UserSecret = {
    name: string;
    value: string;
    tags: Record<string, string>;
    created_at: string;
    updated_at: string;
    description: string;
    version: string;
    value_size_bytes: number;
};

export type UpsertUserSecretResult = {
    version: string;
    updated_at: string;
};

export type ListUserSecretsResult = {
    secrets: UserSecret[];
    next_page_token: string;
    total_count: number;
};

export type DeleteUserSecretResult = {
    deleted: boolean;
};

// Re-export the ClientMetric type
export type ClientMetric = TClientMetric;

/**
 * CompletionResolution describes the resolution of a completion request.
 * - request_id: the request ID of the request
 * - emit_time_sec/nsec: the time the request was returned to vscode (and presumably shown
 *   to the user)
 * - resolve_time_sec/nsec: the time the user accepted or rejected the completion
 * - accepted_idx: the index of the completion that was accepted, or -1 if the completion
 *   was rejected
 */
export type CompletionResolution = {
    request_id: string;
    emit_time_sec: number;
    emit_time_nsec: number;
    resolve_time_sec: number;
    resolve_time_nsec: number;
    accepted_idx: number;
};

/**
 * CodeEditResolution describes the resolution of an edit request.
 * - request_id: the request ID of the request
 * - emit_time_sec/nsec: the time the request was returned to vscode (and presumably shown
 *   to the user)
 * - resolve_time_sec/nsec: the time the user accepted or rejected the completion
 * - is_accepted: whether the edit was accepted or rejected
 * - annotated_text: the text that was annotated by the user
 * - annotated_instruction: the instruction that was annotated by the user
 */
export type CodeEditResolution = {
    request_id: string;
    emit_time_sec: number;
    emit_time_nsec: number;
    resolve_time_sec: number;
    resolve_time_nsec: number;
    is_accepted: boolean;
    annotated_text?: string;
    annotated_instruction?: string;
};

/**
 * InstructionResolution describes the resolution of an instruction request.
 * - request_id: the request ID of the request
 * - is_accepted_chunks: for each chunk shown to the user, was it accepted or rejected
 * - is_accept_all: did the user click accept all without interacting with any individual chunk
 * - is_reject_all: did the user reject all (or close the diff viewer) without interacting with
 * any individual chunk
 * - emit_time_sec/nsec: the time the response was returned to vscode (and presumably shown to the user)
 * - resolve_time_sec/nsec: the time the user finally resolved the instruction
 */
export type InstructionResolution = {
    request_id: string;
    is_accepted_chunks: boolean[];
    is_accept_all: boolean;
    is_reject_all: boolean;
    emit_time_sec: number;
    emit_time_nsec: number;
    resolve_time_sec: number;
    resolve_time_nsec: number;
};

/**
 * SmartPasteResolution describes the resolution of a smart paste request.
 * - request_id: the request ID of the request
 * - is_accepted_chunks: for each chunk shown to the user, was it accepted or rejected
 * - is_accept_all: did the user click accept all without interacting with any individual chunk
 * - is_reject_all: did the user reject all (or close the diff viewer) without interacting with
 * any individual chunk
 * - initial_request_time_sec/nsec: the time the initial request was made
 * - stream_finish_time_sec/nsec: the time the stream finished successfully
 * - apply_time_sec/nsec: the time the user clicked apply
 * - resolve_time_sec/nsec: the time the user resolved the smart paste
 */
export type SmartPasteResolution = {
    request_id: string;
    is_accepted_chunks: boolean[];
    is_accept_all: boolean;
    is_reject_all: boolean;
    initial_request_time_sec: number;
    initial_request_time_nsec: number;
    stream_finish_time_sec: number;
    stream_finish_time_nsec: number;
    apply_time_sec: number;
    apply_time_nsec: number;
    resolve_time_sec: number;
    resolve_time_nsec: number;
};

/**
 * NextEditResolution describes the resolution of a next edit request.
 * - request_id: the request ID of the request
 * - emit_time_sec/nsec: the time the request was returned to vscode (and presumably shown
 *   to the user)
 * - resolve_time_sec/nsec: the time the user accepted or rejected the suggestion
 * - is_accepted: whether the edit was accepted or rejected
 */
export type NextEditResolution = {
    request_id: string;
    emit_time_sec: number;
    emit_time_nsec: number;
    resolve_time_sec: number;
    resolve_time_nsec: number;
    is_accepted: boolean;
};

/**
 * NextEditSessionEvent is a next edit session event that can be logged.
 */
export type NextEditSessionEvent = {
    related_request_id: string | undefined;
    related_suggestion_id: string | undefined;
    event_time_sec: number;
    event_time_nsec: number;
    event_name: NextEditSessionEventName;
    event_source: NextEditSessionEventSource | undefined;
};

/**
 * OnboardingSessionEvent is an onboarding session event that can be logged.
 */
export type OnboardingSessionEvent = {
    event_time_sec: number;
    event_time_nsec: number;
    event_name: OnboardingSessionEventName;
};

export type PreferenceSample = {
    request_ids: string[];

    scores: { [k: string]: number };

    feedback: string;
};

export type Model = {
    name: string;
    suggestedPrefixCharCount: number;
    suggestedSuffixCharCount: number;
    completionTimeoutMs?: number;
    internalName?: string;
};

export type Language = {
    name: string;
    vscodeName: string;
    extensions: string[];
};

export type ModelConfig = {
    defaultModel: string;
    models: Model[];
    languages: Language[];
    featureFlags: FeatureFlags;
    userTier: UserTier;
    user?: {
        id: string;
        email?: string;
    };
};

export type TabSwitchEvent = {
    path: string;
    file_blob_name: string;
};

export type ViewedContentEvent = {
    path: string;
    file_blob_name: string;
    visible_content: string;
    line_start: number;
    line_end: number;
    char_start: number;
    char_end: number;
    timestamp: string; // ISO string format
};

export type RecencyInfo = {
    tab_switch_events?: TabSwitchEvent[];
    recent_changes?: ReplacementText[];
    viewed_contents?: ViewedContentEvent[];
};

export enum ExtensionSessionEventName {
    ConfigurationSnapshot = "configuration-snapshot",
    SourceFolderSnapshot = "source-folder-snapshot",
}

export type ExtensionEventAdditionalData = {
    key: string;
    value: string;
};

export type ExtensionSessionEvent = {
    time_iso: string;
    event_name: ExtensionSessionEventName;
    additional_data?: ExtensionEventAdditionalData[];
};

export type BlobMetadata = KeyValuePair[];

export type ClientCompletionTimline = {
    request_id: string;

    initial_request_time_sec: number;
    initial_request_time_nsec: number;
    api_start_time_sec: number;
    api_start_time_nsec: number;
    api_end_time_sec: number;
    api_end_time_nsec: number;
    emit_time_sec: number;
    emit_time_nsec: number;
};

export type CheckCommandResult = {
    result: boolean;
    desc: string;
};

export type ContainErrorsResult = {
    result: boolean;
    desc: string;
};

export type FilePatch = {
    path: string;
    sourceContent: string;
    targetContent: string;
};

export type FileChangeDesc = {
    path: string;
    desc: string;
    related: boolean;
};

export type FileChunk = {
    path: string;
    content: string;
    start_line: number;
    end_line: number;
};

export type FileFix = {
    path: string;
    change_desc: string;
    code_context: string;
};

export type FixPlanResult = {
    fix_desc: string;
    changes: FileFix[];
};

interface ISmartPasteLIFOQueueRequest {
    requestId: string;
    instruction: string;
    blobs: Blobs;
    chatHistory: Exchange[];
    selectedText: string;
    prefix: string;
    suffix: string;
    pathName: string;
    blobName: string;
    prefixBegin?: number;
    suffixEnd?: number;
    language: string;
    codeBlock: string;
    targetFilePath: string;
    targetFileContent: string;
    contextCodeExchangeRequestId?: string;

    // A Promise that resolves once the initial request is finished
    requestServicedSignal?: Promise<void>;
}

/**
 * APIServer is the interface to the augment API.
 */
export interface APIServer extends SidecarAPIServer {
    sessionId: string;
    getSessionId(): string;
    createRequestId(): string;
    reportError: (
        originalRequestId: string | null,
        sanitizedMessage: string,
        stackTrace: string,
        diagnostics: KeyValuePair[]
    ) => Promise<void>;
    complete: (
        requestId: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string | undefined,
        completionLocation: CompletionLocation | undefined,
        language: string,
        blobs: Blobs,
        recentChanges: ReplacementText[],
        viewedContentEvents: ViewedContentInfo[],
        fileEditEvents?: FileEditEvent[],
        completionTimeoutMs?: number,
        probeOnly?: boolean,
        apiTiming?: APITiming
    ) => Promise<CompletionResult>;
    checkpointBlobs: (blobs: Blobs) => Promise<CheckpointBlobsResult>;
    memorize: (
        pathName: string,
        text: string,
        blobName: string,
        metadata: BlobMetadata,
        timeoutMs?: number
    ) => Promise<MemorizeResult>;
    batchUpload: (blobs: Array<UploadBlob>) => Promise<BatchUploadResult>;
    findMissing: (memoryNames: string[]) => Promise<FindMissingResult>;
    resolveCompletions: (resolutions: CompletionResolution[]) => Promise<void>;
    logCodeEditResolution: (resolution: CodeEditResolution) => Promise<void>;
    logInstructionResolution: (resolution: InstructionResolution) => Promise<void>;
    logSmartPasteResolution: (resolution: SmartPasteResolution) => Promise<void>;
    resolveNextEdits: (resolutions: NextEditResolution[]) => Promise<void>;
    logNextEditSessionEvent: (events: NextEditSessionEvent[]) => Promise<void>;
    logOnboardingSessionEvent: (events: OnboardingSessionEvent[]) => Promise<void>;
    logAgentSessionEvent: (events: AgentSessionEvent[]) => Promise<void>;
    logRemoteAgentSessionEvent: (events: RemoteAgentSessionEvent[]) => Promise<void>;
    logAgentRequestEvent: (events: AgentRequestEvent[]) => Promise<void>;
    logExtensionSessionEvent: (event: ExtensionSessionEvent[]) => Promise<void>;
    logToolUseRequestEvent(events: ToolUseRequestEvent[]): Promise<void>;
    logFeatureVector(events: FeatureVector): Promise<void>;
    recordPreferenceSample: (sample: PreferenceSample) => Promise<void>;
    getModelConfig: () => Promise<ModelConfig>;
    editCode(
        requestId: string,
        instruction: string,
        selectedCode: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string,
        prefixBegin: number,
        suffixEnd: number,
        language: string,
        blobs: Blobs
    ): Promise<CodeEditResult>;
    completionFeedback(feedback: CompletionFeedback): Promise<void>;
    nextEditFeedback(feedback: NextEditFeedback): Promise<void>;
    getAccessToken(
        authURI: string,
        tenantURL: string,
        codeVerifier: string,
        code: string
    ): Promise<string>;
    chat(
        requestId: string,
        message: string,
        chatHistory: Exchange[],
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        model: string | undefined,
        vcsChange: VCSChange,
        recentChanges: ReplacementText[],
        contextCodeExchangeRequestId?: string,
        selectedCode?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        language?: string,
        sessionId?: string,
        disableAutoExternalSources?: boolean,
        userGuidelines?: string,
        workspaceGuidelines?: string,
        toolDefinitions?: ToolDefinition[],
        nodes?: ChatRequestNode[],
        mode?: ChatMode,
        agentMemories?: string,
        personaType?: PersonaType
    ): Promise<ChatResult>;
    chatInstructionStream(
        requestId: string,
        instruction: string,
        blobs: Blobs,
        chatHistory: Exchange[],
        selectedText?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        blobName?: string,
        prefixBegin?: number,
        suffixEnd?: number,
        language?: string,
        userGuidelines?: string,
        workspaceGuidelines?: string,
        contextCodeExchangeRequestId?: string
    ): Promise<AsyncIterable<ChatInstructionStreamResult>>;
    smartPasteStream(
        requestId: string,
        instruction: string,
        blobs: Blobs,
        chatHistory: Exchange[],
        selectedText?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        blobName?: string,
        prefixBegin?: number,
        suffixEnd?: number,
        language?: string,
        codeBlock?: string,
        targetFilePath?: string,
        targetFileContent?: string,
        contextCodeExchangeRequestId?: string
    ): Promise<AsyncIterable<ChatInstructionStreamResult>>;
    generateCommitMessageStream(
        requestId: string,
        commitMessagePromptData: CommitMessagePromptData
    ): Promise<AsyncIterable<GenerateCommitMessageResult>>;
    // The backend types are identical for chat and agent feedback.
    // Split when we actually need to
    chatFeedback(feedback: ChatFeedback): Promise<void>;
    nextEditLocation(
        requestId: string,
        instruction: string,
        pathName: string,
        vcsChange: VCSChange,
        fileEditEvents: FileEditEvent[],
        blobs: Blobs,
        recentChanges: ReplacementText[],
        diagnostics: Diagnostic[],
        numResults: number,
        isSingleFile: boolean
    ): Promise<NextEditLocationResult>;
    nextEditStream(
        request: NextEditStreamRequest
    ): Promise<AsyncIterable<NextEditGenerationResult>>;
    uploadUserEvents(user_event_batch: any): Promise<void>;
    clientMetrics(metrics: Array<ClientMetric>): Promise<void>;
    searchExternalSources(
        query: string,
        sourceTypes: string[]
    ): Promise<SearchExternalSourcesResponse>;
    reportClientCompletionTimelines(timelines: ClientCompletionTimline[]): Promise<void>;

    saveChat(
        conversation_id: string,
        conversation: Exchange[],
        title: string
    ): Promise<SaveChatResult>;
    getImplicitExternalSources(message: string): Promise<SearchExternalSourcesResponse>;
    // All /agents/ APIs are experimental and subject to change.
    agentCodebaseRetrieval(
        requestId: string,
        informationRequest: string,
        blobs: Blobs,
        chatHistory: Exchange[],
        maxOutputLength: number,
        options?: AgentCodebaseRetrievalOptions,
        signal?: AbortSignal
    ): Promise<AgentCodebaseRetrievalResult>;
    agentEditFile(
        requestId: string,
        filePath: string,
        editSummary: string,
        detailedEditDescription: string,
        fileContents: string,
        signal?: AbortSignal
    ): Promise<AgentEditFileResult>;
    listRemoteTools(toolIDs: RemoteToolId[]): Promise<ListRemoteToolsResult>;
    // User Secrets API
    upsertUserSecret(
        requestId: string,
        name: string,
        value: string,
        tags?: Record<string, string>,
        description?: string
    ): Promise<UpsertUserSecretResult>;
    listUserSecrets(
        requestId: string,
        includeValues?: boolean,
        namePattern?: string
    ): Promise<ListUserSecretsResult>;
    deleteUserSecret(requestId: string, name: string): Promise<DeleteUserSecretResult>;
    checkToolSafety(toolId: RemoteToolId, toolInputJson: string): Promise<boolean>;
    runRemoteTool(
        requestId: string,
        toolName: string,
        toolInputJson: string,
        toolId: RemoteToolId,
        extraToolInput: ExtraToolInput | undefined,
        signal: AbortSignal
    ): Promise<RunRemoteToolResult>;
    revokeToolAccess(toolId: RemoteToolId): Promise<RevokeToolAccessResult>;
    readNotifications(): Promise<GetNotificationsResponse>;
    markNotificationAsRead(notificationId: string, actionItemTitle?: string): Promise<void>;
    createRemoteAgent(
        workspaceSetup: RemoteAgentWorkspaceSetup,
        initialRequestDetails: RemoteAgentChatRequestDetails,
        model?: string,
        setupScript?: string,
        isSetupScriptAgent?: boolean
    ): Promise<CreateRemoteAgentResponse>;
    /** Send a chat request to a remote agent. */
    remoteAgentChat(
        remoteAgentId: string,
        chatRequestDetails: RemoteAgentChatRequestDetails,
        timeoutMs?: number
    ): Promise<{ remoteAgentId: string; nodes: ChatResultNode[] }>;
    /** Delete a remote agent. */
    deleteRemoteAgent(remoteAgentId: string): Promise<void>;
    /** Interrupt a remote agent. */
    interruptRemoteAgent(remoteAgentId: string): Promise<RemoteAgentStatus>;
    /** Pause a remote agent workspace. */
    pauseRemoteAgentWorkspace(remoteAgentId: string): Promise<void>;
    /** Resume a remote agent workspace. */
    resumeRemoteAgentWorkspace(remoteAgentId: string): Promise<void>;
    /** Update a remote agent's title. */
    updateRemoteAgentTitle(remoteAgentId: string, newTitle?: string): Promise<RemoteAgent>;
    /** Send a resume hint to a remote agent. */
    resumeHintRemoteAgent(
        remoteAgentId: string,
        hintReason?: RemoteAgentResumeHintReason
    ): Promise<void>;
    /** List all remote agents. */
    listRemoteAgents(): Promise<ListRemoteAgentsResponse>;
    /** Get the chat history for a remote agent. */
    getRemoteAgentChatHistory(
        remoteAgentId: string,
        lastProcessedSequenceId: number
    ): Promise<GetRemoteAgentChatHistoryResponse>;
    /** Setup SSH connection for a remote agent. */
    remoteAgentAddSSHKey(
        remoteAgentId: string,
        publicKeys: string[]
    ): Promise<RemoteAgentAddSSHKeyResponse>;
    /** Get the workspace setup logs for a remote agent. */
    getRemoteAgentWorkspaceLogs(
        remoteAgentId: string,
        lastProcessedStep?: number,
        lastProcessedSequenceId?: number
    ): Promise<RemoteAgentWorkspaceLogsResponse>;
    listGithubReposForAuthenticatedUser(
        page?: number
    ): Promise<ListGithubReposForAuthenticatedUserResponse>;
    listGithubRepoBranches(
        repo: GithubRepo,
        page?: number
    ): Promise<ListGithubRepoBranchesResponse>;
    getGithubRepo(repo: GithubRepo): Promise<{ repo: GithubRepo; error?: string }>;
    isUserGithubConfigured(): Promise<IsUserGithubConfiguredResponse>;
    getSubscriptionInfo(): Promise<GetSubscriptionInfoResponse>;
}

type CompletionPayload = {
    model?: string;
    prompt: string;
    suffix?: string;
    path: string;
    blob_name?: string;
    prefix_begin?: number;
    cursor_position?: number;
    suffix_end?: number;
    lang: string | undefined;
    blobs: BlobsPayload;
    recency_info?: RecencyInfo;
    probe_only?: boolean;
    sequence_id?: number;
    filter_threshold?: number;
    edit_events: FileEditEventsPayload[];
    viewed_content_events?: ViewedContentEvent[];
};

type CheckpointBlobsPayload = {
    blobs: BlobsPayload;
};

type CodeEditPayload = {
    model?: string;
    prefix?: string;
    selected_text: string;
    suffix?: string;
    blobs?: BlobsPayload;
    path?: string;
    instruction: string;
    lang?: string;
    blob_name?: string;
    prefix_begin?: number;
    suffix_end?: number;
    sequence_id?: number;
};

type ChatInstructionStreamPayload = {
    model?: string;
    prefix?: string;
    selected_text?: string;
    suffix?: string;
    path?: string;
    instruction: string;
    lang?: string;
    blob_name?: string;
    prefix_begin?: number;
    suffix_end?: number;
    sequence_id?: number;
    blobs?: BlobsPayload;
    chat_history: Exchange[];
    code_block?: string;
    target_file_path?: string;
    target_file_content?: string;
    context_code_exchange_request_id?: string;
    user_guidelines?: string;
    workspace_guidelines?: string;
};

type GenerateCommitMessagePayload = {
    changed_file_stats?: ChangedFileStats;
    diff?: string;
    relevant_commit_messages: string[];
    example_commit_messages: string[];
};

export type FileEditSingleEventPayload = {
    before_start: number;
    after_start: number;

    before_text: string;
    after_text: string;
};

export type FileEditEventsPayload = {
    path: string;
    before_blob_name: string;
    after_blob_name: string;
    edits: FileEditSingleEventPayload[];
};

export type FileCharRangePayload = {
    path: string;
    char_start: number;
    char_end: number;
};

type NextEditLocationPayload = {
    instruction?: string;
    path?: string;
    vcs_change: VCSChangePayload;
    edit_events: FileEditEventsPayload[];
    blobs?: BlobsPayload;
    recent_changes?: ReplacementText[];
    diagnostics?: Diagnostic[];
    num_results: number;
    is_single_file: boolean;
};

/** Describes request for nextEditStream() using backend types. */
type NextEditStreamPayload = {
    model?: string;
    prefix?: string;
    selected_text?: string;
    suffix?: string;
    selection_begin_char?: number;
    selection_end_char?: number;
    blobs?: BlobsPayload;
    recent_changes?: ReplacementText[];
    diagnostics?: Diagnostic[];
    path?: string;
    instruction: string;
    lang?: string;
    blob_name?: string;
    vcs_change: VCSChangePayload;
    edit_events: FileEditEventsPayload[];
    blocked_locations: FileCharRangePayload[];
    mode: NextEditMode;
    scope: NextEditScope;
    api_version: number;
    sequence_id?: number;
    client_created_at: Date;
    unindexed_edit_events: FileEditEventsPayload[];
    unindexed_edit_events_base_blob_names: string[];
};

/**
 * These Back* types are the result types of the various back-end requests. They use
 * the names defined by the back-end API, which don't conform to the TypeScript naming
 * convention, hence the non-Back* types above.
 */

// One item in a list of completions
export type BackCompletionItem = {
    text: string;
    skipped_suffix: string;
    suffix_replacement_text: string;
    filter_score?: number;
};

// For backward compatibility:
// * `completions` replaces `text`.
// * `unknown_blob_names` replaces `unknown_memory_names`.
// production still uses `text` and PR to replace it with completion_items is ongoing.
// research uses completions and will be switched to completion_items.
// So for now we need to support all 3 fields.
// A well formed result will have at least one of each pair. If it has both, the newer
// field will be used and the older one will be ignored.
export type BackCompletionResult = {
    text: string;
    completions: string[];
    completion_items: BackCompletionItem[];
    unknown_memory_names: string[];
    unknown_blob_names: string[];
    // Added for checkpoint versions, which server may not yet support.
    checkpoint_not_found?: boolean;
    suggested_prefix_char_count?: number;
    suggested_suffix_char_count?: number;
    // The completion request timeout in milliseconds.
    completion_timeout_ms: number;
};

export type BackCheckpointBlobsResult = {
    new_checkpoint_id: string;
};

// For backward compatibility:
// * `blob_name` replaces `mem_object_name`
export type BackMemorizeResult = {
    blob_name: string;
    mem_object_name: string;
};

export type BackFindMissingResult = {
    unknown_memory_names: string[];
    nonindexed_blob_names: string[];
};

// For backward compatibility:
// * `max_upload_size_bytes` replaces `max_memorize_size_bytes`
export type BackModelInfo = {
    name: string;
    suggested_prefix_char_count: number;
    suggested_suffix_char_count: number;
    max_upload_size_bytes?: number;
    max_memorize_size_bytes?: number;
    completion_timeout_ms?: number;
    internal_name?: string;
};

export type BackLanguageInfo = {
    name: string;
    vscode_name: string;
    extensions: string[];
};

export type BackFeatureFlags = {
    tab_switch_events?: boolean;
    git_diff_polling_freq_msec?: number;
    notification_polling_interval_ms?: number;
    checkpoint_blobs_v2?: boolean;
    additional_chat_models?: string;
    vscode_external_sources_in_chat_min_version?: string;
    enable_instructions?: boolean;
    enable_smart_paste?: boolean;
    enable_smart_paste_min_version?: string;
    enable_view_text_document?: boolean;
    checkpoint_blobs?: boolean;
    enable_chat?: boolean;
    enable_uploads_by_language?: boolean;
    small_sync_threshold?: number;
    big_sync_threshold?: number;
    enable_workspace_manager_ui?: boolean;
    enable_external_sources_in_chat?: boolean;
    bypass_language_filter?: boolean;
    enable_workspace_manager_ui_launch?: boolean;
    enable_hindsight?: boolean;
    enable_prompt_enhancer?: boolean;
    max_upload_size_bytes?: number;
    vscode_next_edit_min_version?: string;
    vscode_direct_apply_min_version?: string;
    vscode_flywheel_min_version?: string;
    vscode_share_min_version?: string;
    max_trackable_file_count?: number;
    max_trackable_file_count_without_permission?: number;
    min_uploaded_percentage_without_permission?: number;
    vscode_sources_min_version?: string;
    vscode_chat_hint_decoration_min_version?: string;
    next_edit_debounce_ms?: number;
    enable_completion_file_edit_events?: boolean;
    enable_viewed_content_tracking?: boolean;
    viewed_content_close_range_threshold?: number;
    viewed_content_discrete_jump_threshold?: number;
    viewed_content_min_event_age_ms?: number;
    viewed_content_max_event_age_ms?: number;
    viewed_content_max_tracked_files?: number;
    viewed_content_max_same_file_entries?: number;
    vscode_enable_cpu_profile?: boolean;
    verify_folder_is_source_repo?: boolean;
    refuse_to_sync_home_directories?: boolean;
    enable_file_limits_for_syncing_permission?: boolean;
    enable_chat_mermaid_diagrams?: boolean;
    enable_summary_titles?: boolean;
    smart_paste_precompute_mode?: SmartPastePrecomputeMode;
    vscode_new_threads_menu_min_version?: string;
    vscode_editable_history_min_version?: string;
    vscode_enable_chat_mermaid_diagrams_min_version?: string;

    vscode_use_checkpoint_manager_context_min_version?: string;
    vscode_validate_checkpoint_manager_context?: boolean;
    vscode_design_system_rich_text_editor_min_version?: string;
    allow_client_feature_flag_overrides?: boolean;
    vscode_chat_with_tools_min_version?: string;
    vscode_chat_multimodal_min_version?: string;
    vscode_agent_mode_min_version?: string;
    vscode_agent_mode_min_stable_version?: string;
    enable_agent_tabs?: boolean;
    enable_agent_git_tracker?: boolean;
    remote_agents_resume_hint_available_ttl_days?: number;
    vscode_background_agents_min_version?: string;
    vscode_agent_edit_tool?: string;
    memories_params?: string;
    elo_model_configuration?: string;
    vscode_next_edit_bottom_panel_min_version?: string;
    workspace_guidelines_length_limit?: number;
    user_guidelines_length_limit?: number;
    vscode_rich_checkpoint_info_min_version?: string;
    vscode_chat_stable_prefix_truncation_min_version?: string;
    agent_edit_tool_min_view_size?: number;
    agent_edit_tool_schema_type?: string;
    agent_edit_tool_enable_fuzzy_matching?: boolean;
    agent_edit_tool_fuzzy_match_success_message?: string;
    agent_edit_tool_fuzzy_match_max_diff?: number;
    agent_edit_tool_fuzzy_match_max_diff_ratio?: number;
    agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs?: number;
    vscode_personalities_min_version?: string;
    memory_classification_on_first_token?: boolean;
    agent_edit_tool_instructions_reminder?: boolean;
    agent_edit_tool_show_result_snippet?: boolean;
    agent_edit_tool_max_lines?: number;
    agent_save_file_tool_instructions_reminder?: boolean;
    use_memory_snapshot_manager?: boolean;
    vscode_generate_commit_message_min_version?: string;
    enable_rules?: boolean;
    memories_text_editor_enabled?: boolean;
    enable_model_registry?: boolean;
    model_registry?: string;
    model_info_registry?: string;
    agent_chat_model?: string;
    vscode_task_list_min_version?: string;
    vscode_support_tool_use_start_min_version?: string;
    open_file_manager_v2_enabled?: boolean;
    enable_agent_auto_mode?: boolean;
    enable_agent_swarm_mode?: boolean;
    vscode_remote_agent_ssh_min_version?: string;
    client_announcement?: string;
    grep_search_tool_enable?: boolean;
    grep_search_tool_timelimit_sec?: number;
    grep_search_tool_output_chars_limit?: number;
    grep_search_tool_num_context_lines?: number;
    history_summary_min_version?: string;
    history_summary_params?: string;
    enable_new_threads_list?: boolean;
    enable_untruncated_content_storage?: boolean;
    max_lines_terminal_process_output_after_truncation?: number;
    max_lines_terminal_process_output?: number;
    truncation_footer_addition_text?: string;
    enable_commit_indexing?: boolean;
    max_commits_to_index?: number;
    enable_exchange_storage?: boolean;
    enable_tool_use_state_storage?: boolean;
    retry_chat_stream_timeouts?: boolean;
    remote_agent_current_workspace?: boolean;
    enable_memory_retrieval?: boolean;
    enable_swarm_mode?: boolean;
    enable_grouped_tools?: boolean;
    vscode_min_version?: string;
    enable_parallel_tools?: boolean;
    enable_native_remote_mcp?: boolean;
    enable_sentry?: boolean;
    webview_error_sampling_rate?: number;
    webview_trace_sampling_rate?: number;
    agent_view_tool_params?: string;
    vscode_terminal_strategy?: string;
    non_dismissible_banner_test_treatment?: ExperimentTreatment;
};

export type BackGetModelsResult = {
    default_model: string;
    max_upload_size_bytes?: number;
    models: BackModelInfo[];
    languages: BackLanguageInfo[];
    feature_flags?: BackFeatureFlags;
    user_tier?: "UNKNOWN" | "COMMUNITY_TIER" | "PROFESSIONAL_TIER" | "ENTERPRISE_TIER";
    user?: {
        id: string;
        email?: string;
    };
};

export type BackCodeEditResult = {
    text: string;
    unknown_blob_names?: string[];
    checkpoint_not_found?: boolean;
};

export type BackChatInstructionStreamResult = {
    text: string;
    unknown_blob_names?: string[];
    checkpoint_not_found: boolean;
    replacement_text?: string;
    replacement_old_text?: string;
    replacement_start_line?: number;
    replacement_end_line?: number;
};

export type BackLineRange = {
    start: number;
    stop: number;
};

export type BackFileLocation = {
    path: string;
    range: BackLineRange;
    header?: string;
};

export type BackScoredFileHunk = {
    file_location: BackFileLocation;
    original_code: string;
    updated_code: string;
    truncation_char?: number;
    localization_score: number;
    editing_score: number;
    change_description: string;
    diff_spans?: BackDiffSpan[];
};

export type BackLocation = {
    path: string;
    range: BackLineRange;
};

export type BackScored = {
    item: BackLocation;
    score: number;
    debug_info?: string;
};

export type BackNextEditLocationResult = {
    candidate_locations: BackScored[];
    unknown_blob_names: string[];
    checkpoint_not_found?: boolean;
    critical_errors?: string[];
};

export type BackCharRange = {
    start: number;
    stop: number;
};

export type BackDiffSpan = {
    original: BackCharRange;
    updated: BackCharRange;
};

export type BackNextEditResult = {
    suggestion_id: string;
    path: string;
    blob_name: string;
    char_start: number;
    char_end: number;
    existing_code: string;
    suggested_code: string;
    truncation_char: number;
    change_description: string;
    diff_spans: BackDiffSpan[];
    editing_score: number;
    localization_score: number;
    editing_score_threshold?: number;
};

export type BackNextEditGenerationResult = {
    next_edit: BackNextEditResult;
    unknown_blob_names?: string[];
    checkpoint_not_found?: boolean;
};

export type BackGenerateCommitMessageResult = {
    text: string;
};

export type BackTextReplacement = {
    description: string;
    path: string;
    text: string;
    old_text: string;
    start_line: number;
    end_line: number;
    sequence_id: number;
    old_blob_name?: string;
};

export type TextReplacement = {
    description: string;
    path: string;
    text: string;
    oldText: string;
    startLine: number;
    endLine: number;
    sequenceId: number;
    oldBlobName?: string;
};

export type UploadBlob = {
    pathName: string;
    text: string;
    blobName: string;
    metadata: BlobMetadata;
};

export type BackRemoteAgentChatRequest = {
    remote_agent_id: string;
    request_details: RemoteAgentChatRequestDetails;
};

export type BackRemoteAgentChatResponse = {
    remote_agent_id: string;
    nodes: ChatResultNode[];
};

class ConversionFailure extends Error {
    constructor(errMsg: string, response: string) {
        super(`Conversion failure: ${errMsg}. Response = ${response}`);
    }
}

class APIServerImpl extends SidecarAPIServerImpl implements APIServer {
    private _sequenceId = new SequenceID();

    protected readonly _logger: AugmentLogger = getLogger("AugmentExtension");

    constructor(
        protected _configListener: AugmentConfigListener,
        private _auth: AuthSessionStore,
        public readonly sessionId: string,
        private readonly _userAgent: string,
        private readonly _fetchFunction: FetchFunction
    ) {
        super(sessionId, _userAgent, _fetchFunction);
    }

    public getSessionId(): string {
        return this.sessionId;
    }

    public createRequestId(): string {
        return uuidv4();
    }

    protected async callApi<T extends {} | void>(
        requestId: string,
        config: AugmentConfig,
        apiEndpoint: string,
        body: Record<string, any>,
        convert?: (json: {}) => T,
        baseURL?: string,
        requestTimeoutMs?: number,
        apiTiming?: APITiming,
        signal?: AbortSignal,
        dropAndReplaceSurrogates: boolean = false
    ): Promise<T> {
        let token = config.apiToken;
        let isSessionToken = false;

        if (this._auth.useOAuth) {
            // We have OAuth config and the user does NOT have an API token.
            const session = await this._auth.getSession();
            if (session) {
                token = session.accessToken;
                isSessionToken = true;
                if (!baseURL) {
                    baseURL = session.tenantURL;
                }
            }
        } else {
            // Either OAuth is not set up yet OR the user has an API token.
            if (!baseURL) {
                baseURL = config.completionURL;
            }
        }

        if (!baseURL) {
            throw new Error("Please configure Augment API URL");
        }
        let apiURL: URL | undefined;
        try {
            apiURL = new URL(apiEndpoint, baseURL);
        } catch (e) {
            this._logger.error("Augment API URL is invalid:", e);
            throw new InvalidCompletionURLError();
        }
        if (!apiURL.protocol.startsWith("http")) {
            throw new Error("Augment API URL must start with 'http://' or 'https://'");
        }

        // Step zero, prepare to do the API call
        const body_json = safeJsonStringify(body, this._logger, dropAndReplaceSurrogates);

        const timeoutMs = requestTimeoutMs ?? APIServerImpl.defaultRequestTimeoutMs;
        const timeoutSignal = AbortSignal.timeout(timeoutMs);
        const method = "POST";
        // Step one do the api call and get respose
        let response: Response;

        let apiStart: number;
        let apiEnd: number;
        try {
            let headers: { [key: string]: string } = {
                "Content-Type": "application/json",
                "User-Agent": this._userAgent,
                "x-request-id": `${requestId}`,
                "x-request-session-id": `${this.sessionId}`,
                // Version negotiation: if the server's minimum supported version is greater than
                // this value, it will return a 406 Not Acceptable error. The client should then
                // warn the user to upgrade. The source of truth for versions is the ApiVersion
                // enum in services/api_proxy/public_api.proto.
                "x-api-version": "2",
            };
            if (token) {
                headers["Authorization"] = `Bearer ${token}`;
            }

            apiStart = Date.now();
            response = await withTimeout(
                this._fetchFunction(apiURL.toString(), {
                    method,
                    headers,
                    body: body_json,
                    signal: abortSignalAny([timeoutSignal].concat(signal ? [signal] : [])),
                }),
                timeoutMs
            );
            apiEnd = Date.now();
        } catch (e) {
            if (e instanceof Error) {
                this._logger.error(
                    `API request ${requestId} to ${apiURL.toString()} failed: ${getErrmsg(e, true)}`
                );
                throw APIError.transientIssue(e);
            } else {
                this._logger.debug(`API request ${requestId} to ${apiURL.toString()} failed`);
            }
            throw e;
        }

        if (apiTiming) {
            apiTiming.rpcStart = apiStart;
            apiTiming.rpcEnd = apiEnd;
        }

        // Step 2 verify response
        if (!response!.ok) {
            if (response.status === 499) {
                // Don't log 499 errors as there are lots of them and they are generally not of
                // interest.
                throw new APIError(APIStatus.cancelled, "Request cancelled");
            }
            if (response.status === 401 && isSessionToken) {
                // Removing the session token will reload the extension,
                // forcing the user to sign in.
                void this._auth.removeSession();
            }

            if (config.enableDebugFeatures) {
                // print body of response
                this._logger.error(
                    `API request ${requestId} to ${apiURL.toString()} failed: ${await response.clone().text()}`
                );
            }

            this._logger.error(
                `API request ${requestId} to ${apiURL.toString()} ` +
                    `response ${response.status}: ${response.statusText}`
            );

            const apiError = await APIError.fromResponse(response);
            if (apiError.status === APIStatus.augmentTooLarge) {
                this._logger.debug(`object size is ${getPropertySizes(body)} `);
            }

            throw apiError;
        }

        let json: any;
        try {
            if (response.headers.get("content-length") === "0") {
                return void 0 as T;
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            json = await response.json();
        } catch (e) {
            this._logger.error(
                `API request ${requestId} to ${apiURL.toString()} ` +
                    `failed to convert response to json: ${(e as Error).message}`
            );
            throw e;
        }
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            return convert ? convert(json) : (json as unknown as T);
        } catch (e) {
            throw new ConversionFailure(getErrmsg(e), JSON.stringify(json));
        }
    }

    /**
     * Convert a BackCompletionItem to a CompletionItem.
     *
     * Also check that the fields are of the correct type.
     *
     * @param item BackCompletionItem
     * @returns CompletionItem
     */
    private _toCompletionItem(item: BackCompletionItem): CompletionItem {
        // verify that field text is present and is a string, otherwise throw
        if (typeof item.text !== "string") {
            throw new Error(`Completion item text is not a string: ${JSON.stringify(item)}`);
        }
        // verify that the other two fields, if present, are also strings
        if (item.skipped_suffix !== undefined && typeof item.skipped_suffix !== "string") {
            throw new Error(
                `Completion item skipped suffix is not a string: ${JSON.stringify(item)}`
            );
        }
        if (
            item.suffix_replacement_text !== undefined &&
            typeof item.suffix_replacement_text !== "string"
        ) {
            throw new Error(
                `Completion item suffix replacement text is not a string: ${JSON.stringify(item)}`
            );
        }
        return {
            text: item.text,
            suffixReplacementText: item.suffix_replacement_text ?? "",
            skippedSuffix: item.skipped_suffix ?? "",
            filterScore: item.filter_score ?? undefined,
        };
    }

    // Recent version of research and prod can have completion in any one of the
    // three fields: completion_items, completions and text.  We will try to parse
    // in this order.
    private _extractCompletions(resp: BackCompletionResult): CompletionItem[] {
        // It has the newest format: array of items
        if (Array.isArray(resp.completion_items)) {
            return resp.completion_items.map((item) => this._toCompletionItem(item));
        }
        // Previous protocol, a list of strings
        // The result must have at least one of completions or text.
        if (Array.isArray(resp.completions)) {
            const completions = toStringArray(
                "BackCompletionResult",
                "completions",
                resp.completions
            );
            return completions.map((text) => {
                return {
                    text,
                    suffixReplacementText: "",
                    skippedSuffix: "",
                };
            });
        }
        // The earliest format, just a string. Prod does this currently but will soon change.
        return [
            {
                text: toString("BackCompletionResult", "text", resp.text),
                suffixReplacementText: "",
                skippedSuffix: "",
            },
        ];
    }

    private toCompleteResult(resp: BackCompletionResult): CompletionResult {
        const completionItems = this._extractCompletions(resp);

        // The result must have at least one of unknown_blob_names or
        // unknown_memory_names.
        const unknownBlobNames = toStringArray(
            "BackCompletionResult",
            "unknown_blob_names/unknown_memory_names",
            resp.unknown_blob_names ?? resp.unknown_memory_names
        );

        // Backend may not return the checkpoint_not_found field if it's not yet
        // supported, so fallback to a default value.
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackCompletionResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );
        return {
            completionItems,
            unknownBlobNames,
            checkpointNotFound,
            suggestedPrefixCharCount: resp.suggested_prefix_char_count,
            suggestedSuffixCharCount: resp.suggested_suffix_char_count,
            completionTimeoutMs: resp.completion_timeout_ms,
        };
    }

    private toCheckpointBlobsResult(resp: BackCheckpointBlobsResult): CheckpointBlobsResult {
        const newCheckpointId = toString(
            "BackCheckpointBlobsResult",
            "new_checkpoint_id",
            resp.new_checkpoint_id
        );

        return {
            newCheckpointId,
        };
    }

    // complete() issues a completion request to the back end.
    //  - requestId: The request's request id
    //  - prefix: Completion's prefix text
    //  - suffix: Completion's suffix text
    //  - pathName: path name of the completion's target blob
    //  - blobName: blob name of the completion's target blob
    //  - completionLocation: location information within the target blob
    //  - blobs: the blobs to include as context for the completion
    //  - recencyInfo: information about recent events
    //  - fileEditEvents: information about recent file edits
    public async complete(
        requestId: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string | undefined,
        completionLocation: CompletionLocation | undefined,
        language: string,
        blobs: Blobs,
        recentChanges: ReplacementText[],
        viewedContent: ViewedContentInfo[],
        fileEditEvents?: FileEditEvent[],
        completionTimeoutMs?: number,
        probeOnly?: boolean,
        apiTiming?: APITiming
    ): Promise<CompletionResult> {
        const config = this._configListener.config;

        const recencyInfo: RecencyInfo = {
            recent_changes: recentChanges,
            viewed_contents: this.toViewedContentPayload(viewedContent),
        };

        const payload: CompletionPayload = {
            model: config.modelName,
            prompt: prefix,
            suffix,
            path: pathName,
            blob_name: blobName,
            prefix_begin: completionLocation?.prefixBegin,
            cursor_position: completionLocation?.cursorPosition,
            suffix_end: completionLocation?.suffixEnd,
            lang: language,
            blobs: blobsToBlobsPayload(blobs),
            recency_info: recencyInfo,
            probe_only: probeOnly,
            sequence_id: this._sequenceId.next(),
            filter_threshold: config.completions.filterThreshold,
            edit_events: this.toFileDiffsPayload(fileEditEvents ?? []),
        };
        return await this.callApi<CompletionResult>(
            requestId,
            config,
            "completion",
            payload,
            (json) => this.toCompleteResult(json as BackCompletionResult),
            undefined,
            completionTimeoutMs,
            apiTiming,
            undefined,
            true // Drop and replace surrogates
        );
    }

    public async checkpointBlobs(blobs: Blobs): Promise<CheckpointBlobsResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        const payload: CheckpointBlobsPayload = {
            blobs: blobsToBlobsPayload(blobs),
        };
        const result = await this.callApi<CheckpointBlobsResult>(
            requestId,
            config,
            "checkpoint-blobs",
            payload,
            (json) => this.toCheckpointBlobsResult(json as BackCheckpointBlobsResult)
        );
        for (const url of this.getUniqueExtraURLs()) {
            // TODO: Remove this when we switch to production.
            const extraURLResult = await this.callApi<CheckpointBlobsResult>(
                requestId,
                config,
                "checkpoint-blobs",
                payload,
                (json) => this.toCheckpointBlobsResult(json as BackCheckpointBlobsResult),
                url
            );
            if (extraURLResult.newCheckpointId !== result.newCheckpointId) {
                this._logger.error(
                    `Checkpoint blobs API returned different checkpoint IDs for ${url}`
                );
            }
        }
        return result;
    }

    private convertToCodeEditResult(resp: BackCodeEditResult): CodeEditResult {
        // Backend may not return the unknown_blob_names field if it's not yet
        // supported, so fallback to an empty array.
        const unknownBlobNames =
            resp.unknown_blob_names === undefined
                ? []
                : toStringArray(
                      "BackCodeEditResult",
                      "unknown_blob_names",
                      resp.unknown_blob_names
                  );

        // Backend may not return the checkpoint_not_found field if it's not yet
        // supported, so fallback to a default value.
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackCodeEditResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );
        return {
            unknownBlobNames,
            checkpointNotFound,
            modifiedCode: resp.text,
        };
    }

    public async editCode(
        requestId: string,
        instruction: string,
        selectedCode: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string,
        prefixBegin: number,
        suffixEnd: number,
        language: string,
        blobs: Blobs
    ): Promise<CodeEditResult> {
        const config = this._configListener.config;
        const model = config.codeInstruction.model;
        const payload: CodeEditPayload = {
            model,
            instruction,
            prefix,
            selected_text: selectedCode,
            suffix,
            blob_name: blobName,
            prefix_begin: prefixBegin,
            suffix_end: suffixEnd,
            lang: language,
            path: pathName,
            blobs: blobsToBlobsPayload(blobs),
            sequence_id: this._sequenceId.next(),
        };
        return await this.callApi<CodeEditResult>(
            requestId,
            config,
            "edit",
            payload,
            (json) => this.convertToCodeEditResult(json as BackCodeEditResult),
            undefined,
            120000
        );
    }

    public async chat(
        requestId: string,
        message: string,
        chatHistory: Exchange[],
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        model: string | undefined,
        vcsChange: VCSChange,
        recentChanges: ReplacementText[],
        contextCodeExchangeRequestId?: string,
        selectedCode?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        language?: string,
        _sessionId?: string,
        disableAutoExternalSources?: boolean,
        userGuidelines?: string,
        workspaceGuidelines?: string,
        toolDefinitions?: ToolDefinition[],
        nodes?: ChatRequestNode[],
        mode?: ChatMode,
        agentMemories?: string,
        personaType?: PersonaType
    ): Promise<ChatResult> {
        const config = this._configListener.config;
        if (mode === ChatMode.agent) {
            model = model ?? config.agent.model;
        } else {
            model = model ?? config.chat.model;
        }
        const payload: ChatPayload = {
            model,
            path: pathName,
            prefix: prefix,
            selected_code: selectedCode,
            suffix: suffix,
            message: message,
            chat_history: chatHistory,
            lang: language,
            blobs: blobsToBlobsPayload(blobs),
            user_guided_blobs: userGuidedBlobs,
            external_source_ids: externalSourceIds,
            enable_preference_collection: config.preferenceCollection.enable,
            context_code_exchange_request_id: contextCodeExchangeRequestId,
            vcs_change: toVCSChangePayload(vcsChange),
            recency_info_recent_changes: recentChanges,
            feature_detection_flags: {
                support_raw_output: true,
            },
            disable_auto_external_sources: disableAutoExternalSources,
            user_guidelines: userGuidelines,
            workspace_guidelines: workspaceGuidelines,
            tool_definitions: toolDefinitions ?? [],
            nodes: nodes ?? [],
            mode: mode ?? ChatMode.chat,
            agent_memories: agentMemories,
            persona_type: personaType,
        };
        return await this.callApi<ChatResult>(
            requestId,
            config,
            "chat",
            payload,
            (json) => toChatResult(json as BackChatResult),
            // If chat URL is not configured, the function will use the completion URL.
            config.chat.url,
            300000
        );
    }

    private toChatInstructionStreamResult(
        resp: BackChatInstructionStreamResult
    ): ChatInstructionStreamResult {
        // Backend may not return the unknown_blob_names field if it's not yet
        // supported, so fallback to an empty array.
        const unknownBlobNames =
            resp.unknown_blob_names === undefined
                ? []
                : toStringArray(
                      "BackChatInstructionStreamResult",
                      "unknown_blob_names",
                      resp.unknown_blob_names
                  );

        // Backend may not return the checkpoint_not_found field if it's not yet
        // supported, so fallback to a default value.
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackChatInstructionStreamResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );
        return {
            text: resp.text,
            unknownBlobNames,
            checkpointNotFound,
            replacementText: resp.replacement_text,
            replacementOldText: resp.replacement_old_text,
            replacementStartLine: resp.replacement_start_line,
            replacementEndLine: resp.replacement_end_line,
        };
    }

    public async chatInstructionStream(
        requestId: string,
        instruction: string,
        blobs: Blobs,
        chatHistory: Exchange[],
        selectedText: string = "",
        prefix: string = "",
        suffix: string = "",
        pathName: string = "",
        blobName: string = "",
        prefixBegin: number | undefined = undefined,
        suffixEnd: number | undefined = undefined,
        language: string = "",
        userGuidelines?: string,
        workspaceGuidelines?: string,
        contextCodeExchangeRequestId?: string
    ): Promise<AsyncIterable<ChatInstructionStreamResult>> {
        const config = this._configListener.config;

        // We are using the same API call for instruction and smart paste.
        // The only difference is that smart paste requests have an empty instruction.
        const isSmartPasteRequest = instruction.length === 0;

        // If chat URL is not configured (undefined), the function will use the completion URL.
        let url: string | undefined;
        let model: string | undefined;

        if (isSmartPasteRequest) {
            url = config.smartPaste.url ?? config.chat.url;
            model = config.smartPaste.model;
        } else {
            url = config.chat.url;
            model = config.instructions.model;
        }

        const payload: ChatInstructionStreamPayload = {
            model: model,
            prefix: prefix,
            selected_text: selectedText,
            suffix: suffix,
            path: pathName,
            instruction: instruction,
            lang: language,
            blob_name: blobName,
            prefix_begin: prefixBegin,
            suffix_end: suffixEnd,
            blobs: blobsToBlobsPayload(blobs),
            chat_history: chatHistory,
            context_code_exchange_request_id: contextCodeExchangeRequestId,
            user_guidelines: userGuidelines,
            workspace_guidelines: workspaceGuidelines,
        };
        return await this.callApiStream<ChatInstructionStreamResult>(
            requestId,
            config,
            "instruction-stream",
            payload,
            this.toChatInstructionStreamResult.bind(this),
            url,
            120000
        );
    }

    private _smartPasteQueue: LIFOWorkQueue<
        ISmartPasteLIFOQueueRequest,
        AsyncIterable<ChatInstructionStreamResult>
    > = new LIFOWorkQueue<ISmartPasteLIFOQueueRequest, AsyncIterable<ChatInstructionStreamResult>>(
        this._processSingleSmartPasteRequest.bind(this)
    );

    public async smartPasteStream(
        requestId: string,
        instruction: string,
        blobs: Blobs,
        chatHistory: Exchange[],
        selectedText: string = "",
        prefix: string = "",
        suffix: string = "",
        pathName: string = "",
        blobName: string = "",
        prefixBegin: number | undefined = undefined,
        suffixEnd: number | undefined = undefined,
        language: string = "",
        codeBlock: string = "",
        targetFilePath: string = "",
        targetFileContent: string = "",
        contextCodeExchangeRequestId?: string
    ): Promise<AsyncIterable<ChatInstructionStreamResult>> {
        const request: ISmartPasteLIFOQueueRequest = {
            requestId,
            instruction,
            blobs,
            chatHistory,
            selectedText,
            prefix,
            suffix,
            pathName,
            blobName,
            prefixBegin,
            suffixEnd,
            language,
            codeBlock,
            targetFilePath,
            targetFileContent,
            contextCodeExchangeRequestId,
        };

        // Add the request to the LIFO queue and return the promise.
        return this._smartPasteQueue.insertAndKick(request);
    }

    // Overload signatures
    private async _processSingleSmartPasteRequest(
        request: ISmartPasteLIFOQueueRequest
    ): Promise<AsyncIterable<ChatInstructionStreamResult>>;
    private async _processSingleSmartPasteRequest(request: undefined): Promise<void>;
    private async _processSingleSmartPasteRequest(
        request: ISmartPasteLIFOQueueRequest | undefined
    ): Promise<AsyncIterable<ChatInstructionStreamResult> | void> {
        if (request === undefined) {
            return;
            // If this request is already being serviced, wait for it to finish.
        } else if (request.requestServicedSignal !== undefined) {
            return await request.requestServicedSignal;
        }

        const config = this._configListener.config;

        // We are using the same API call for instruction and smart paste.
        // The only difference is that smart paste requests have an empty instruction.
        const isSmartPasteRequest = request.instruction.length === 0;

        // If chat URL is not configured (undefined), the function will use the completion URL.
        let url: string | undefined;
        let model: string | undefined;

        if (isSmartPasteRequest) {
            url = config.smartPaste.url ?? config.chat.url;
            model = config.smartPaste.model;
        } else {
            url = config.chat.url;
        }

        const payload: ChatInstructionStreamPayload = {
            model: model,
            prefix: request.prefix,
            selected_text: request.selectedText,
            suffix: request.suffix,
            path: request.pathName,
            instruction: request.instruction,
            lang: request.language,
            blob_name: request.blobName,
            prefix_begin: request.prefixBegin,
            suffix_end: request.suffixEnd,
            blobs: blobsToBlobsPayload(request.blobs),
            chat_history: request.chatHistory,
            code_block: request.codeBlock,
            target_file_path: request.targetFilePath,
            target_file_content: request.targetFileContent,
            context_code_exchange_request_id: request.contextCodeExchangeRequestId,
        };

        // Add in a new request into the work queue that blocks it from doing anything until the entire stream is done.
        // This makes sure that, while the stream is being received, no other requests are sent.
        let resolveRequestServicedSignal: () => void;
        request.requestServicedSignal = new Promise((resolve) => {
            resolveRequestServicedSignal = resolve;
        });
        void this._smartPasteQueue.insertAndKick(request);

        // When the stream is done, resolve the promise so the next request can be processed.
        const newStream = async function* (this: APIServerImpl) {
            try {
                yield* await this.callApiStream<ChatInstructionStreamResult>(
                    request.requestId,
                    config,
                    "smart-paste-stream",
                    payload,
                    this.toChatInstructionStreamResult.bind(this),
                    url,
                    120000
                );
            } finally {
                resolveRequestServicedSignal();
            }
        }.bind(this)();
        return newStream;
    }

    toGenerateCommitMessageResult(
        resp: BackGenerateCommitMessageResult
    ): GenerateCommitMessageResult {
        return {
            text: resp.text,
        };
    }

    public async generateCommitMessageStream(
        requestId: string,
        commitMessagePromptData: CommitMessagePromptData
    ): Promise<AsyncIterable<GenerateCommitMessageResult>> {
        const config = this._configListener.config;
        const payload: GenerateCommitMessagePayload = {
            changed_file_stats: commitMessagePromptData.changedFileStats,
            diff: commitMessagePromptData.diff,
            relevant_commit_messages:
                commitMessagePromptData.generatedCommitMessageSubrequest.relevant_commit_messages,
            example_commit_messages:
                commitMessagePromptData.generatedCommitMessageSubrequest.example_commit_messages,
        };
        return await this.callApiStream<GenerateCommitMessageResult>(
            requestId,
            config,
            "generate-commit-message-stream",
            payload,
            this.toGenerateCommitMessageResult.bind(this),
            undefined,
            120000
        );
    }

    async createRemoteAgent(
        workspaceSetup: RemoteAgentWorkspaceSetup,
        initialRequestDetails: RemoteAgentChatRequestDetails,
        model?: string,
        setupScript?: string,
        isSetupScriptAgent?: boolean
    ): Promise<CreateRemoteAgentResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload: CreateRemoteAgentRequest = {
            workspace_setup: workspaceSetup,
            initial_request_details: {
                request_nodes: initialRequestDetails.request_nodes,
                user_guidelines:
                    initialRequestDetails.user_guidelines ?? config.chat.userGuidelines,
                workspace_guidelines: initialRequestDetails.workspace_guidelines ?? "",
                agent_memories: initialRequestDetails.agent_memories ?? "",
                mcp_servers: initialRequestDetails.mcp_servers,
            },
            model: model ?? config.modelName,
            setup_script: setupScript,
            is_setup_script_agent: isSetupScriptAgent || false,
        };

        // Add token from session if using OAuth
        if (this._auth.useOAuth) {
            const session = await this._auth.getSession();
            if (session) {
                payload.token = session.accessToken;
            }
        }

        // eslint-disable-next-line no-console
        console.log("Calling /remote-agents/create with payload: ", payload);

        return await this.callApi<CreateRemoteAgentResponse>(
            requestId,
            config,
            "remote-agents/create",
            payload
        );
    }

    public async remoteAgentChat(
        remoteAgentId: string,
        chatRequestDetails: RemoteAgentChatRequestDetails,
        timeoutMs?: number
    ): Promise<{ remoteAgentId: string; nodes: ChatResultNode[] }> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload: BackRemoteAgentChatRequest = {
            remote_agent_id: remoteAgentId,
            request_details: {
                request_nodes: chatRequestDetails.request_nodes,
                user_guidelines: chatRequestDetails.user_guidelines ?? config.chat.userGuidelines,
                workspace_guidelines: chatRequestDetails.workspace_guidelines ?? "",
                agent_memories: chatRequestDetails.agent_memories ?? "",
                model_id: chatRequestDetails.model_id,
                mcp_servers: chatRequestDetails.mcp_servers,
            },
        };

        const response = await this.callApi<BackRemoteAgentChatResponse>(
            requestId,
            config,
            "remote-agents/chat",
            payload,
            undefined,
            undefined,
            timeoutMs
        );

        return {
            remoteAgentId: response.remote_agent_id,
            nodes: response.nodes,
        };
    }

    public async deleteRemoteAgent(remoteAgentId: string): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
        };

        return await this.callApi<void>(requestId, config, "remote-agents/delete", payload);
    }

    public async interruptRemoteAgent(remoteAgentId: string): Promise<RemoteAgentStatus> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
        };

        const response = await this.callApi<RemoteAgentStatus>(
            requestId,
            config,
            "remote-agents/interrupt",
            payload
        );

        return response;
    }

    public async pauseRemoteAgentWorkspace(remoteAgentId: string): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
        };

        return await this.callApi<void>(requestId, config, "remote-agents/pause", payload);
    }

    public async resumeRemoteAgentWorkspace(remoteAgentId: string): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
        };

        return await this.callApi<void>(requestId, config, "remote-agents/resume", payload);
    }

    public async updateRemoteAgentTitle(
        remoteAgentId: string,
        newTitle?: string
    ): Promise<RemoteAgent> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
            new_title: newTitle,
        };

        return await this.callApi<RemoteAgent>(requestId, config, "remote-agents/update", payload);
    }

    public async resumeHintRemoteAgent(
        remoteAgentId: string,
        hintReason: RemoteAgentResumeHintReason = RemoteAgentResumeHintReason.viewingAgent
    ): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
            hint_reason: hintReason,
        };

        return await this.callApi<void>(requestId, config, "remote-agents/resume-hint", payload);
    }

    public async listRemoteAgents(): Promise<ListRemoteAgentsResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const response = await this.callApi<ListRemoteAgentsResponse>(
            requestId,
            config,
            "remote-agents/list",
            {}
        );

        return response;
    }

    public async getRemoteAgentChatHistory(
        remoteAgentId: string,
        lastProcessedSequenceId: number
    ): Promise<GetRemoteAgentChatHistoryResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            remote_agent_id: remoteAgentId,
            last_processed_sequence_id: lastProcessedSequenceId,
        };

        const response = await this.callApi<GetRemoteAgentChatHistoryResponse>(
            requestId,
            config,
            "remote-agents/get-chat-history",
            payload
        );

        return response;
    }

    public async remoteAgentAddSSHKey(
        remoteAgentId: string,
        publicKeys: string[]
    ): Promise<RemoteAgentAddSSHKeyResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload: RemoteAgentAddSSHKeyRequest = {
            remote_agent_id: remoteAgentId,
            public_keys: publicKeys,
        };

        // When ssh'ing after resuming the remote agent workspace, it may take a
        // moment for the beachhead to be fully ready so we need a longer timeout
        const response = await this.callApi<RemoteAgentAddSSHKeyResponse>(
            requestId,
            config,
            "remote-agents/add-ssh-key",
            payload,
            undefined,
            undefined,
            60_000 // 1 minute timeout
        );

        return response;
    }

    public async getRemoteAgentWorkspaceLogs(
        remoteAgentId: string,
        lastProcessedStep?: number,
        lastProcessedSequenceId?: number
    ): Promise<RemoteAgentWorkspaceLogsResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload: RemoteAgentWorkspaceLogsRequest = {
            remote_agent_id: remoteAgentId,
            last_processed_step: lastProcessedStep,
            last_processed_sequence_id: lastProcessedSequenceId,
        };

        const response = await this.callApi<RemoteAgentWorkspaceLogsResponse>(
            requestId,
            config,
            "remote-agents/logs",
            payload
        );

        return response;
    }

    public async listGithubReposForAuthenticatedUser(
        page?: number
    ): Promise<ListGithubReposForAuthenticatedUserResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload: ListGithubReposForAuthenticatedUserRequest = {
            page,
        };

        const response = await this.callApi<ListGithubReposForAuthenticatedUserResponse>(
            requestId,
            config,
            "github/list-repos",
            payload,
            undefined,
            undefined,
            90_000 // 90 second timeout
        );

        return response;
    }

    public async listGithubRepoBranches(
        repo: GithubRepo,
        page?: number
    ): Promise<ListGithubRepoBranchesResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload: ListGithubRepoBranchesRequest = {
            repo: {
                owner: repo.owner,
                name: repo.name,
            },
            page: page,
        };

        const response = await this.callApi<ListGithubRepoBranchesResponse>(
            requestId,
            config,
            "github/list-branches",
            payload,
            undefined,
            undefined,
            90_000 // 90 second timeout
        );

        return response;
    }

    public async getGithubRepo(repo: GithubRepo): Promise<{ repo: GithubRepo; error?: string }> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const payload = {
            repo: {
                owner: repo.owner,
                name: repo.name,
            },
        };

        try {
            const response = await this.callApi<{ repo: GithubRepo }>(
                requestId,
                config,
                "github/get-repo",
                payload
            );

            return response;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get GitHub repo:", error);
            return {
                repo: repo,
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    public async isUserGithubConfigured(): Promise<IsUserGithubConfiguredResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const response = await this.callApi<IsUserGithubConfiguredResponse>(
            requestId,
            config,
            "github/is-user-configured",
            {}
        );

        return response;
    }

    public async getSubscriptionInfo(): Promise<GetSubscriptionInfoResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        const response = await this.callApi<GetSubscriptionInfoResponse>(
            requestId,
            config,
            "subscription-info",
            {}
        );

        return response;
    }

    private toBackTextReplacement(replacement: TextReplacement): BackTextReplacement {
        return {
            description: replacement.description,
            path: replacement.path,
            text: replacement.text,
            old_text: replacement.oldText,
            start_line: replacement.startLine,
            end_line: replacement.endLine,
            sequence_id: replacement.sequenceId,
            old_blob_name: replacement.oldBlobName,
        };
    }

    private toFileDiffsPayload(fileDiffs: FileEditEvent[]): FileEditEventsPayload[] {
        return fileDiffs.map((diff) => {
            return {
                path: diff.path,
                before_blob_name: diff.beforeBlobName,
                after_blob_name: diff.afterBlobName,
                edits: diff.edits.map((event: SingleEdit) => ({
                    before_start: event.beforeStart,
                    after_start: event.afterStart,
                    before_text: event.beforeText,
                    after_text: event.afterText,
                })),
            };
        });
    }

    private toViewedContentPayload(viewedContents: ViewedContentInfo[]): ViewedContentEvent[] {
        return viewedContents.map((content) => {
            return {
                path: content.relPathName,
                file_blob_name: content.blobName,
                visible_content: content.visibleContent,
                line_start: content.lineStart,
                line_end: content.lineEnd,
                char_start: content.charStart,
                char_end: content.charEnd,
                timestamp: content.timestamp.toISOString(),
            };
        });
    }

    private toFileCharRangePayload(fileDiffs: BlockedPathAndRange[]): FileCharRangePayload[] {
        return fileDiffs
            .filter((diff) => diff.charStart !== undefined && diff.charStop !== undefined)
            .map((diff) => {
                return {
                    path: diff.path,
                    char_start: diff.charStart!,
                    char_end: diff.charStop!,
                };
            });
    }

    private toNextEditLocationResult(resp: BackNextEditLocationResult): NextEditLocationResult {
        verifyArray("BackNextEditLocationResult", "candidate_locations", resp.candidate_locations);
        const unknownBlobNames =
            resp.unknown_blob_names === undefined
                ? []
                : toStringArray(
                      "BackNextEditLocationResult",
                      "unknown_blob_names",
                      resp.unknown_blob_names
                  );
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackNextEditResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );
        const criticalErrors =
            resp.critical_errors === undefined || resp.critical_errors === null
                ? []
                : toStringArray("BackNextEditResult", "critical_errors", resp.critical_errors);
        const resultArray: Scored[] = [];
        for (const s of resp.candidate_locations) {
            const range: LineRange = {
                start: toNumber("BackLineRange", "start", s.item.range.start),
                stop: toNumber("BackLineRange", "stop", s.item.range.stop),
            };
            const location: Location = {
                path: toString("BackLocation", "path", s.item.path),
                range: range,
            };
            const scored: Scored = {
                item: location,
                score: toNumber("BackScored", "score", s.score),
                debugInfo: s.debug_info,
            };
            resultArray.push(scored);
        }
        return {
            candidateLocations: resultArray,
            unknownBlobNames: unknownBlobNames,
            checkpointNotFound: checkpointNotFound,
            criticalErrors: criticalErrors,
        };
    }

    public async nextEditLocation(
        requestId: string,
        instruction: string,
        pathName: string,
        vcsChange: VCSChange,
        fileEditEvents: FileEditEvent[],
        blobs: Blobs,
        recentChanges: ReplacementText[],
        diagnostics: Diagnostic[],
        numResults: number,
        isSingleFile: boolean
    ): Promise<NextEditLocationResult> {
        const config = this._configListener.config;

        const payload: NextEditLocationPayload = {
            instruction: instruction,
            path: pathName,
            vcs_change: toVCSChangePayload(vcsChange),
            edit_events: this.toFileDiffsPayload(fileEditEvents),
            blobs: blobsToBlobsPayload(blobs),
            recent_changes: recentChanges,
            diagnostics: diagnostics,
            num_results: numResults,
            is_single_file: isSingleFile,
        };
        return await this.callApi<NextEditLocationResult>(
            requestId,
            config,
            "next_edit_loc",
            payload,
            (json) => this.toNextEditLocationResult(json as BackNextEditLocationResult),
            config.nextEdit.locationUrl,
            120000,
            undefined,
            undefined,
            true // Drop and replace surrogates
        );
    }

    private convertToNextEditGenerationResult(
        resp: BackNextEditGenerationResult
    ): NextEditGenerationResult {
        // Backend may not return the unknown_blob_names field if it's not yet
        // supported, so fallback to an empty array.
        const unknownBlobNames =
            resp.unknown_blob_names === undefined
                ? []
                : toStringArray(
                      "BackNextEditGenerationResult",
                      "unknown_blob_names",
                      resp.unknown_blob_names
                  );
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackNextEditResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );

        const result = this.convertToNextEditResult(resp.next_edit);

        return {
            result,
            unknownBlobNames,
            checkpointNotFound,
        };
    }

    private convertToNextEditResult(resp: BackNextEditResult): NextEditResult {
        const suggestionId = toString("BackNextEditResult", "suggestion_id", resp.suggestion_id);
        const path = toString("BackNextEditResult", "path", resp.path);
        const blobName = toString("BackNextEditResult", "blob_name", resp.blob_name);
        const charStart = toNumber("BackNextEditResult", "char_start", resp.char_start);
        const charEnd = toNumber("BackNextEditResult", "char_end", resp.char_end);
        const existingCode = toString("BackNextEditResult", "existing_code", resp.existing_code);
        const suggestedCode = toString("BackNextEditResult", "suggested_code", resp.suggested_code);
        const truncationChar =
            resp.truncation_char === undefined || resp.truncation_char === null
                ? undefined
                : toNumber("BackNextEditResult", "truncation_char", resp.truncation_char);
        const changeDescription =
            resp.change_description === undefined
                ? ""
                : toString("BackNextEditResult", "change_description", resp.change_description);
        const diffSpans = resp.diff_spans?.map((s) => {
            return {
                original: {
                    start: toNumber("BackCharRange", "start", s.original.start),
                    stop: toNumber("BackCharRange", "stop", s.original.stop),
                },
                updated: {
                    start: toNumber("BackCharRange", "start", s.updated.start),
                    stop: toNumber("BackCharRange", "stop", s.updated.stop),
                },
            };
        });
        const editingScore = toNumber("BackNextEditResult", "editing_score", resp.editing_score);
        const localizationScore = toNumber(
            "BackNextEditResult",
            "localization_score",
            resp.localization_score
        );
        const editingScoreThreshold =
            resp.editing_score_threshold === undefined
                ? 1.0
                : toNumber(
                      "BackNextEditResult",
                      "editing_score_threshold",
                      resp.editing_score_threshold
                  );
        return {
            suggestionId,
            path,
            blobName,
            charStart,
            charEnd,
            existingCode,
            suggestedCode,
            truncationChar,
            changeDescription,
            diffSpans,
            editingScore,
            localizationScore,
            editingScoreThreshold,
        };
    }

    public async nextEditStream(
        request: NextEditStreamRequest
    ): Promise<AsyncIterable<NextEditGenerationResult>> {
        const config = this._configListener.config;

        const model = config.nextEdit.model;
        const prefixLength = request.prefix ? utf32Length(request.prefix) : undefined;
        const payload: NextEditStreamPayload = {
            model,
            instruction: request.instruction ?? "",
            prefix: request.prefix,
            selected_text: request.selectedCode,
            suffix: request.suffix,
            selection_begin_char: prefixLength,
            selection_end_char:
                request.prefix !== undefined && request.selectedCode !== undefined
                    ? prefixLength! + utf32Length(request.selectedCode)
                    : undefined,
            blob_name: request.blobName,
            lang: request.language,
            path: request.pathName?.relPath,
            blobs: (request.blobs && blobsToBlobsPayload(request.blobs)) ?? {
                checkpoint_id: undefined,
                added_blobs: [],
                deleted_blobs: [],
            },
            recent_changes: request.recentChanges,
            diagnostics: request.diagnostics,
            vcs_change: toVCSChangePayload({ workingDirectory: [], commits: [] }),
            edit_events: this.toFileDiffsPayload(request.fileEditEvents ?? []),
            blocked_locations: this.toFileCharRangePayload(request.blockedLocations ?? []),
            mode: request.mode,
            scope: request.scope,
            api_version: 3,
            sequence_id: this._sequenceId.next(),
            client_created_at: request.clientCreatedAt,
            unindexed_edit_events: this.toFileDiffsPayload(request.unindexedEditEvents),
            unindexed_edit_events_base_blob_names: request.unindexedEditEventsBaseBlobNames,
        };
        return await this.callApiStream<NextEditGenerationResult>(
            request.requestId,
            config,
            "next-edit-stream",
            payload,
            this.convertToNextEditGenerationResult.bind(this),
            config.nextEdit.generationUrl,
            120000,
            undefined,
            undefined,
            true // Drop and replace surrogates
        );
    }

    // TODO: Remove this when we switch to production.
    protected getUniqueExtraURLs(): Set<string> {
        const config = this._configListener.config;
        const extraURLs = new Set<string>();
        if (config.nextEdit.url) {
            extraURLs.add(config.nextEdit.url);
        }
        if (config.nextEdit.locationUrl) {
            extraURLs.add(config.nextEdit.locationUrl);
        }
        if (config.nextEdit.generationUrl) {
            extraURLs.add(config.nextEdit.generationUrl);
        }
        if (config.chat.url) {
            extraURLs.add(config.chat.url);
        }
        return extraURLs;
    }

    private toMemorizeResult(resp: BackMemorizeResult): MemorizeResult {
        const blobName =
            resp.blob_name !== undefined
                ? toString("BackMemorizeResult", "blob_name", resp.blob_name)
                : toString("BackMemorizeResult", "mem_object_name", resp.mem_object_name);
        return { blobName };
    }

    public async memorize(
        pathName: string,
        text: string,
        blobName: string,
        metadata: BlobMetadata,
        timeoutMs?: number
    ): Promise<MemorizeResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        const result = await this.callApi<MemorizeResult>(
            requestId,
            config,
            "memorize",
            {
                model: config.modelName,
                path: pathName,
                t: text,
                blob_name: blobName,
                metadata: metadata,
                timeout_ms: timeoutMs,
            },
            (json) => this.toMemorizeResult(json as BackMemorizeResult)
        );
        for (const url of this.getUniqueExtraURLs()) {
            // TODO: Remove this when we switch to production.
            await this.callApi<MemorizeResult>(
                requestId,
                config,
                "memorize",
                {
                    model: config.modelName,
                    path: pathName,
                    t: text,
                    blob_name: blobName,
                    metadata: metadata,
                },
                (json) => this.toMemorizeResult(json as BackMemorizeResult),
                url
            );
        }
        return result;
    }

    private toBatchUploadResult(resp: any): BatchUploadResult {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        return { blobNames: resp.blob_names };
    }

    public async batchUpload(blobs: Array<UploadBlob>): Promise<BatchUploadResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        try {
            const result = await this.callApi<BatchUploadResult>(
                requestId,
                config,
                "batch-upload",
                {
                    blobs: blobs.map((blob) => {
                        return {
                            blob_name: blob.blobName,
                            path: blob.pathName,
                            content: blob.text,
                        };
                    }),
                },
                this.toBatchUploadResult.bind(this)
            );
            for (const url of this.getUniqueExtraURLs()) {
                // TODO: Remove this when we switch to production.
                await this.callApi<BatchUploadResult>(
                    requestId,
                    config,
                    "batch-upload",
                    {
                        blobs: blobs.map((blob) => {
                            return {
                                blob_name: blob.blobName,
                                path: blob.pathName,
                                content: blob.text,
                            };
                        }),
                    },
                    this.toBatchUploadResult.bind(this),
                    url
                );
            }
            return result;
        } catch (err: any) {
            // Unimplemented may occur on the research backend
            if (!APIError.isAPIErrorWithStatus(err, APIStatus.unimplemented)) {
                throw err;
            }

            const names: string[] = [];
            for (const blob of blobs) {
                const result = await this.memorize(
                    blob.pathName,
                    blob.text,
                    blob.blobName,
                    blob.metadata
                );
                names.push(result.blobName);
            }
            return {
                blobNames: names,
            };
        }
    }

    private toFindMissingResult(resp: BackFindMissingResult): FindMissingResult {
        return {
            unknownBlobNames: toStringArray(
                "BackFindMissingResult",
                "unknown_memory_names",
                resp.unknown_memory_names
            ),
            nonindexedBlobNames: toStringArray(
                "BackFindMissingResult",
                "nonindexed_blob_names",
                resp.nonindexed_blob_names
            ),
        };
    }

    public async findMissing(memoryNames: string[]): Promise<FindMissingResult> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        const model = config.modelName;
        const memories = [...memoryNames].sort();
        const result = await this.callApi<FindMissingResult>(
            requestId,
            config,
            "find-missing",
            {
                model,
                mem_object_names: memories,
            },
            (json) => this.toFindMissingResult(json as BackFindMissingResult)
        );
        // If the extra URLs are set, we union their result into the main one.
        // This ensures that all the servers will eventually get all blobs
        // at the cost of doing unneeded uploads to some servers.
        // However, this is only temporary code and will only happen for those
        // of us with these extra URLs set.
        for (const url of this.getUniqueExtraURLs()) {
            // TODO: Remove this when we switch to production.
            let toAdd = await this.callApi<FindMissingResult>(
                requestId,
                config,
                "find-missing",
                {
                    model,
                    mem_object_names: memories,
                },
                (json) => this.toFindMissingResult(json as BackFindMissingResult),
                url
            );
            result.unknownBlobNames = result.unknownBlobNames.concat(toAdd.unknownBlobNames);
            result.nonindexedBlobNames = result.nonindexedBlobNames.concat(
                toAdd.nonindexedBlobNames
            );
        }
        return result;
    }

    public async resolveCompletions(resolutions: CompletionResolution[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(requestId, config, "resolve-completions", {
            client_name: "vscode-extension",
            resolutions,
        });
    }

    public async logCodeEditResolution(resolution: CodeEditResolution): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(requestId, config, "resolve-edit", {
            client_name: "vscode-extension",
            ...resolution,
        });
    }

    /**
     * Track smart paste resolutions
     * this is for instruction-based smart paste, not the research "smart-paste" route
     */
    public async logSmartPasteResolution(resolution: SmartPasteResolution): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(requestId, config, "resolve-smart-paste", {
            client_name: "vscode-extension",
            ...resolution,
        });
    }

    public async logInstructionResolution(resolution: InstructionResolution): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(requestId, config, "resolve-instruction", {
            client_name: "vscode-extension",
            ...resolution,
        });
    }

    public async resolveNextEdits(resolutions: NextEditResolution[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "resolve-next-edit",
            {
                client_name: "vscode-extension",
                resolutions,
            },
            undefined,
            config.nextEdit.url
        );
    }

    public async logFeatureVector(vector: FeatureVector): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "report-feature-vector",
            {
                client_name: "vscode-extension",
                feature_vector: vector,
            },
            undefined
        );
    }

    public async logNextEditSessionEvent(events: NextEditSessionEvent[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "record-session-events",
            {
                client_name: "vscode-extension",
                events: events.map((e) => ({
                    time: new Date(
                        e.event_time_sec * 1000 + e.event_time_nsec / 1000000
                    ).toISOString(),
                    event: {
                        next_edit_session_event: { ...e, user_agent: this._userAgent },
                    },
                })),
            },
            undefined,
            config.nextEdit.url
        );
    }

    public async logOnboardingSessionEvent(events: OnboardingSessionEvent[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "record-session-events",
            {
                client_name: "vscode-extension",
                events: events.map((e) => ({
                    time: new Date(
                        e.event_time_sec * 1000 + e.event_time_nsec / 1000000
                    ).toISOString(),
                    event: {
                        onboarding_session_event: { ...e, user_agent: this._userAgent },
                    },
                })),
            },
            undefined
        );
    }

    public async logAgentSessionEvent(events: AgentSessionEvent[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "record-session-events",
            {
                client_name: "vscode-extension",
                events: events.map((e) => ({
                    time: new Date(
                        e.event_time_sec * 1000 + e.event_time_nsec / 1000000
                    ).toISOString(),
                    event: { agent_session_event: { ...e, user_agent: this._userAgent } },
                })),
            },
            undefined
        );
    }

    public async logRemoteAgentSessionEvent(events: RemoteAgentSessionEvent[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "record-session-events",
            {
                client_name: "vscode-extension",
                events: events.map((e) => ({
                    time: new Date(
                        e.event_time_sec * 1000 + e.event_time_nsec / 1000000
                    ).toISOString(),
                    event: {
                        remote_agent_session_event: { ...e, user_agent: this._userAgent },
                    },
                })),
            },
            undefined
        );
    }

    public async logAgentRequestEvent(events: AgentRequestEvent[]): Promise<void> {
        const eventsByRequestId = new Map<string, AgentRequestEvent[]>();
        for (const event of events) {
            // Get or initialize the events array for this request_id
            const eventsForRequestId = eventsByRequestId.get(event.request_id) || [];
            if (!eventsByRequestId.has(event.request_id)) {
                eventsByRequestId.set(event.request_id, eventsForRequestId);
            }

            // Create a modified event without the request_id field
            const { request_id: _, ...eventWithoutRequestId } = event;
            eventsForRequestId.push(eventWithoutRequestId as AgentRequestEvent);
        }
        for (const [requestId, eventsForRequestId] of eventsByRequestId) {
            const config = this._configListener.config;
            await this.callApi<void>(
                requestId,
                config,
                "record-request-events",
                {
                    events: eventsForRequestId.map((e) => ({
                        time: new Date(
                            e.event_time_sec * 1000 + e.event_time_nsec / 1000000
                        ).toISOString(),
                        event: {
                            agent_request_event: { ...e, user_agent: this._userAgent },
                        },
                    })),
                },
                undefined
            );
        }
    }

    public async logExtensionSessionEvent(events: ExtensionSessionEvent[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(
            requestId,
            config,
            "record-session-events",
            {
                client_name: "vscode-extension",
                events: events.map((e) => ({
                    time: e.time_iso,
                    event: {
                        extension_session_event: { ...e, user_agent: this._userAgent },
                    },
                })),
            },
            undefined
        );
    }

    public async logToolUseRequestEvent(events: ToolUseRequestEvent[]): Promise<void> {
        // Gather all the events that are associated with their respective request IDs together in a request ID -> events[] mapping
        const eventsByRequestId = new Map<string, ToolUseRequestEvent[]>();
        for (const event of events) {
            const eventsForRequestId = eventsByRequestId.get(event.requestId);
            if (eventsForRequestId === undefined) {
                eventsByRequestId.set(event.requestId, [event]);
            } else {
                eventsForRequestId.push(event);
            }
        }
        // Post API calls for each request ID
        for (const [requestId, eventsForRequestId] of eventsByRequestId) {
            const config = this._configListener.config;
            await this.callApi<void>(
                requestId,
                config,
                "record-request-events",
                {
                    events: eventsForRequestId.map((e) => {
                        const toolInput = JSON.stringify(e.toolInput);
                        const tool_use_data: ToolUseRequestEventPayload = {
                            tool_name: e.toolName,
                            tool_use_id: e.toolUseId,
                            tool_output_is_error: e.toolOutputIsError,
                            tool_run_duration_ms: e.toolRunDurationMs,
                            tool_input: toolInput,
                            is_mcp_tool: e.isMcpTool,
                            conversation_id: e.conversationId,
                            chat_history_length: e.chatHistoryLength,
                            tool_request_id: e.toolRequestId,
                            tool_output_len: e.toolOutputLen,
                            tool_input_len: toolInput.length,
                        };
                        return {
                            time: new Date(
                                e.eventTimeSec * 1000 + e.eventTimeNsec / 1000000
                            ).toISOString(),
                            event: { tool_use_data: tool_use_data },
                        };
                    }),
                },
                undefined
            );
        }
    }

    public async recordPreferenceSample(sample: PreferenceSample): Promise<void> {
        // This is ID of the sample itself, don't confuse with IDs inside the sample
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(requestId, config, "record-preference-sample", {
            client_name: "vscode-extension",
            ...sample,
        });
    }

    private toModel(resp: BackModelInfo): Model {
        const completionTimeoutMs =
            resp.completion_timeout_ms !== undefined
                ? toNumber("BackModelInfo", "completion_timeout_ms", resp.completion_timeout_ms)
                : undefined;
        return {
            name: toString("BackModelInfo", "name", resp.name),
            suggestedPrefixCharCount: toNumber(
                "BackModelInfo",
                "suggested_prefix_char_count",
                resp.suggested_prefix_char_count
            ),
            suggestedSuffixCharCount: toNumber(
                "BackModelInfo",
                "suggested_suffix_char_count",
                resp.suggested_suffix_char_count
            ),
            completionTimeoutMs,
            internalName:
                resp.internal_name &&
                toString("BackModelInfo", "internal_name", resp.internal_name),
        };
    }

    private toLanguage(resp: BackLanguageInfo): Language {
        const name = toString("BackLanguageInfo", "name", resp.name);
        const vscodeName = toString("BackLanguageInfo", "vscodeName", resp.vscode_name);
        verifyArray("BackLanguageInfo", "extensions", resp.extensions);
        const extensions: string[] = [];
        for (const extension of resp.extensions) {
            extensions.push(toString("BackLanguageInfo", "extensions", extension));
        }
        return { name, vscodeName, extensions };
    }

    private toGetModelsResult(resp: BackGetModelsResult): ModelConfig {
        const defaultModel = toString("BackGetModelsResult", "default_model", resp.default_model);

        verifyArray("BackGetModelsResult", "models", resp.models);
        const models: Model[] = [];
        for (const modelInfo of resp.models) {
            models.push(this.toModel(modelInfo));
        }

        // Extract feature flags
        const featureFlags = defaultFeatureFlags;

        if (resp.feature_flags !== undefined) {
            let git_diff_freq = resp.feature_flags.git_diff_polling_freq_msec;
            if (git_diff_freq !== undefined && git_diff_freq > 0) {
                featureFlags.gitDiff = true;
                featureFlags.gitDiffPollingFrequencyMSec = git_diff_freq;
            }

            let notification_polling_interval = resp.feature_flags.notification_polling_interval_ms;
            if (notification_polling_interval !== undefined && notification_polling_interval >= 0) {
                featureFlags.notificationPollingIntervalMs = notification_polling_interval;
            }
            if (resp.feature_flags.small_sync_threshold !== undefined) {
                featureFlags.smallSyncThreshold = resp.feature_flags.small_sync_threshold;
            }
            if (resp.feature_flags.big_sync_threshold !== undefined) {
                featureFlags.bigSyncThreshold = resp.feature_flags.big_sync_threshold;
            }
            if (resp.feature_flags.enable_workspace_manager_ui_launch !== undefined) {
                featureFlags.enableWorkspaceManagerUi =
                    resp.feature_flags.enable_workspace_manager_ui_launch;
            }
            if (resp.feature_flags.enable_instructions !== undefined) {
                featureFlags.enableInstructions = resp.feature_flags.enable_instructions;
            }
            if (resp.feature_flags.enable_smart_paste !== undefined) {
                featureFlags.enableSmartPaste = resp.feature_flags.enable_smart_paste;
            }
            if (resp.feature_flags.enable_smart_paste_min_version !== undefined) {
                featureFlags.enableSmartPasteMinVersion =
                    resp.feature_flags.enable_smart_paste_min_version;
            }
            if (resp.feature_flags.enable_view_text_document !== undefined) {
                featureFlags.enableViewTextDocument = resp.feature_flags.enable_view_text_document;
            }
            if (resp.feature_flags.bypass_language_filter !== undefined) {
                featureFlags.bypassLanguageFilter = resp.feature_flags.bypass_language_filter;
            }
            if (resp.feature_flags.additional_chat_models !== undefined) {
                featureFlags.additionalChatModels = resp.feature_flags.additional_chat_models;
            }
            if (resp.feature_flags.enable_hindsight !== undefined) {
                featureFlags.enableHindsight = resp.feature_flags.enable_hindsight;
            }
            if (resp.feature_flags.max_upload_size_bytes !== undefined) {
                featureFlags.maxUploadSizeBytes = resp.feature_flags.max_upload_size_bytes;
            }

            if (resp.feature_flags.vscode_next_edit_min_version !== undefined) {
                featureFlags.vscodeNextEditMinVersion =
                    resp.feature_flags.vscode_next_edit_min_version;
            }
            if (resp.feature_flags.vscode_flywheel_min_version !== undefined) {
                featureFlags.vscodeFlywheelMinVersion =
                    resp.feature_flags.vscode_flywheel_min_version;
            }
            if (resp.feature_flags.vscode_external_sources_in_chat_min_version !== undefined) {
                featureFlags.vscodeExternalSourcesInChatMinVersion =
                    resp.feature_flags.vscode_external_sources_in_chat_min_version;
            }
            if (resp.feature_flags.vscode_share_min_version !== undefined) {
                featureFlags.vscodeShareMinVersion = resp.feature_flags.vscode_share_min_version;
            }
            if (resp.feature_flags.max_trackable_file_count !== undefined) {
                featureFlags.maxTrackableFileCount = resp.feature_flags.max_trackable_file_count;
            }
            if (resp.feature_flags.max_trackable_file_count_without_permission !== undefined) {
                featureFlags.maxTrackableFileCountWithoutPermission =
                    resp.feature_flags.max_trackable_file_count_without_permission;
            }
            if (resp.feature_flags.min_uploaded_percentage_without_permission !== undefined) {
                featureFlags.minUploadedPercentageWithoutPermission =
                    resp.feature_flags.min_uploaded_percentage_without_permission;
            }
            if (resp.feature_flags.vscode_sources_min_version !== undefined) {
                featureFlags.vscodeSourcesMinVersion =
                    resp.feature_flags.vscode_sources_min_version;
            }
            if (resp.feature_flags.vscode_chat_hint_decoration_min_version !== undefined) {
                featureFlags.vscodeChatHintDecorationMinVersion =
                    resp.feature_flags.vscode_chat_hint_decoration_min_version;
            }
            if (resp.feature_flags.next_edit_debounce_ms !== undefined) {
                featureFlags.nextEditDebounceMs = resp.feature_flags.next_edit_debounce_ms;
            }
            if (resp.feature_flags.enable_completion_file_edit_events !== undefined) {
                featureFlags.enableCompletionFileEditEvents =
                    resp.feature_flags.enable_completion_file_edit_events;
            }
            if (resp.feature_flags.enable_viewed_content_tracking !== undefined) {
                featureFlags.enableViewedContentTracking =
                    resp.feature_flags.enable_viewed_content_tracking;
            }
            if (resp.feature_flags.viewed_content_close_range_threshold !== undefined) {
                featureFlags.viewedContentCloseRangeThreshold =
                    resp.feature_flags.viewed_content_close_range_threshold;
            }
            if (resp.feature_flags.viewed_content_discrete_jump_threshold !== undefined) {
                featureFlags.viewedContentDiscreteJumpThreshold =
                    resp.feature_flags.viewed_content_discrete_jump_threshold;
            }
            if (resp.feature_flags.viewed_content_min_event_age_ms !== undefined) {
                featureFlags.viewedContentMinEventAgeMs =
                    resp.feature_flags.viewed_content_min_event_age_ms;
            }
            if (resp.feature_flags.viewed_content_max_event_age_ms !== undefined) {
                featureFlags.viewedContentMaxEventAgeMs =
                    resp.feature_flags.viewed_content_max_event_age_ms;
            }
            if (resp.feature_flags.viewed_content_max_tracked_files !== undefined) {
                featureFlags.viewedContentMaxTrackedFiles =
                    resp.feature_flags.viewed_content_max_tracked_files;
            }
            if (resp.feature_flags.viewed_content_max_same_file_entries !== undefined) {
                featureFlags.viewedContentMaxSameFileEntries =
                    resp.feature_flags.viewed_content_max_same_file_entries;
            }
            if (resp.feature_flags.vscode_enable_cpu_profile !== undefined) {
                featureFlags.vscodeEnableCpuProfile = resp.feature_flags.vscode_enable_cpu_profile;
            }
            if (resp.feature_flags.verify_folder_is_source_repo !== undefined) {
                featureFlags.verifyFolderIsSourceRepo =
                    resp.feature_flags.verify_folder_is_source_repo;
            }
            if (resp.feature_flags.refuse_to_sync_home_directories !== undefined) {
                featureFlags.refuseToSyncHomeDirectories =
                    resp.feature_flags.refuse_to_sync_home_directories;
            }
            if (resp.feature_flags.enable_file_limits_for_syncing_permission !== undefined) {
                featureFlags.enableFileLimitsForSyncingPermission =
                    resp.feature_flags.enable_file_limits_for_syncing_permission;
            }
            if (resp.feature_flags.enable_chat_mermaid_diagrams !== undefined) {
                featureFlags.enableChatMermaidDiagrams =
                    resp.feature_flags.enable_chat_mermaid_diagrams;
            }
            if (resp.feature_flags.enable_summary_titles !== undefined) {
                featureFlags.enableSummaryTitles = resp.feature_flags.enable_summary_titles;
            }
            if (resp.feature_flags.smart_paste_precompute_mode !== undefined) {
                featureFlags.smartPastePrecomputeMode =
                    resp.feature_flags.smart_paste_precompute_mode;
            }
            if (resp.feature_flags.vscode_new_threads_menu_min_version !== undefined) {
                featureFlags.vscodeNewThreadsMenuMinVersion =
                    resp.feature_flags.vscode_new_threads_menu_min_version;
            }
            if (resp.feature_flags.vscode_editable_history_min_version !== undefined) {
                featureFlags.vscodeEditableHistoryMinVersion =
                    resp.feature_flags.vscode_editable_history_min_version;
            }
            if (resp.feature_flags.vscode_enable_chat_mermaid_diagrams_min_version !== undefined) {
                featureFlags.vscodeEnableChatMermaidDiagramsMinVersion =
                    resp.feature_flags.vscode_enable_chat_mermaid_diagrams_min_version;
            }
            if (
                resp.feature_flags.vscode_use_checkpoint_manager_context_min_version !== undefined
            ) {
                featureFlags.useCheckpointManagerContextMinVersion =
                    resp.feature_flags.vscode_use_checkpoint_manager_context_min_version;
            }
            if (resp.feature_flags.vscode_validate_checkpoint_manager_context !== undefined) {
                featureFlags.validateCheckpointManagerContext =
                    resp.feature_flags.vscode_validate_checkpoint_manager_context;
            }
            if (
                resp.feature_flags.vscode_design_system_rich_text_editor_min_version !== undefined
            ) {
                featureFlags.vscodeDesignSystemRichTextEditorMinVersion =
                    resp.feature_flags.vscode_design_system_rich_text_editor_min_version;
            }
            if (resp.feature_flags.allow_client_feature_flag_overrides !== undefined) {
                featureFlags.allowClientFeatureFlagOverrides =
                    resp.feature_flags.allow_client_feature_flag_overrides;
            }
            if (resp.feature_flags.vscode_chat_with_tools_min_version !== undefined) {
                featureFlags.vscodeChatWithToolsMinVersion =
                    resp.feature_flags.vscode_chat_with_tools_min_version;
            }
            if (resp.feature_flags.vscode_agent_mode_min_version !== undefined) {
                featureFlags.vscodeAgentModeMinVersion =
                    resp.feature_flags.vscode_agent_mode_min_version;
            }
            if (resp.feature_flags.vscode_agent_mode_min_stable_version !== undefined) {
                featureFlags.vscodeAgentModeMinStableVersion =
                    resp.feature_flags.vscode_agent_mode_min_stable_version;
            }
            if (resp.feature_flags.vscode_background_agents_min_version !== undefined) {
                featureFlags.vscodeBackgroundAgentsMinVersion =
                    resp.feature_flags.vscode_background_agents_min_version;
            }
            if (resp.feature_flags.vscode_agent_edit_tool !== undefined) {
                featureFlags.vscodeAgentEditTool = resp.feature_flags.vscode_agent_edit_tool;
            }
            if (resp.feature_flags.memories_params !== undefined) {
                try {
                    featureFlags.memoriesParams = JSON.parse(
                        resp.feature_flags.memories_params
                    ) as { [key: string]: string | number | boolean };
                } catch (error) {
                    this._logger.error(`Parsing of "memories_params" failed.`);
                }
            }
            if (resp.feature_flags.elo_model_configuration !== undefined) {
                try {
                    featureFlags.eloModelConfiguration = JSON.parse(
                        resp.feature_flags.elo_model_configuration
                    ) as EloModelConfiguration;
                } catch (error) {
                    this._logger.error(`Parsing of "elo_model_configuration" failed.`);
                }
            }
            if (resp.feature_flags.vscode_chat_stable_prefix_truncation_min_version !== undefined) {
                featureFlags.vscodeChatStablePrefixTruncationMinVersion =
                    resp.feature_flags.vscode_chat_stable_prefix_truncation_min_version;
            }
            if (resp.feature_flags.vscode_direct_apply_min_version !== undefined) {
                featureFlags.vscodeDirectApplyMinVersion =
                    resp.feature_flags.vscode_direct_apply_min_version;
            }
            if (resp.feature_flags.vscode_next_edit_bottom_panel_min_version !== undefined) {
                featureFlags.vscodeNextEditBottomPanelMinVersion =
                    resp.feature_flags.vscode_next_edit_bottom_panel_min_version;
            }
            if (resp.feature_flags.vscode_chat_multimodal_min_version !== undefined) {
                featureFlags.vscodeChatMultimodalMinVersion =
                    resp.feature_flags.vscode_chat_multimodal_min_version;
            }
            if (resp.feature_flags.workspace_guidelines_length_limit !== undefined) {
                featureFlags.workspaceGuidelinesLengthLimit =
                    resp.feature_flags.workspace_guidelines_length_limit;
            }
            if (resp.feature_flags.user_guidelines_length_limit !== undefined) {
                featureFlags.userGuidelinesLengthLimit =
                    resp.feature_flags.user_guidelines_length_limit;
            }
            if (resp.feature_flags.vscode_rich_checkpoint_info_min_version !== undefined) {
                featureFlags.vscodeRichCheckpointInfoMinVersion =
                    resp.feature_flags.vscode_rich_checkpoint_info_min_version;
            }
            if (resp.feature_flags.agent_edit_tool_min_view_size !== undefined) {
                featureFlags.agentEditToolMinViewSize =
                    resp.feature_flags.agent_edit_tool_min_view_size;
            }
            if (resp.feature_flags.agent_edit_tool_schema_type !== undefined) {
                featureFlags.agentEditToolSchemaType =
                    resp.feature_flags.agent_edit_tool_schema_type;
            }
            if (resp.feature_flags.agent_edit_tool_enable_fuzzy_matching !== undefined) {
                featureFlags.agentEditToolEnableFuzzyMatching =
                    resp.feature_flags.agent_edit_tool_enable_fuzzy_matching;
            }
            if (resp.feature_flags.agent_edit_tool_fuzzy_match_success_message !== undefined) {
                featureFlags.agentEditToolFuzzyMatchSuccessMessage =
                    resp.feature_flags.agent_edit_tool_fuzzy_match_success_message;
            }
            if (resp.feature_flags.agent_edit_tool_fuzzy_match_max_diff !== undefined) {
                featureFlags.agentEditToolFuzzyMatchMaxDiff =
                    resp.feature_flags.agent_edit_tool_fuzzy_match_max_diff;
            }
            if (resp.feature_flags.agent_edit_tool_fuzzy_match_max_diff_ratio !== undefined) {
                featureFlags.agentEditToolFuzzyMatchMaxDiffRatio =
                    resp.feature_flags.agent_edit_tool_fuzzy_match_max_diff_ratio;
            }
            if (
                resp.feature_flags
                    .agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs !== undefined
            ) {
                featureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs =
                    resp.feature_flags.agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs;
            }
            if (resp.feature_flags.vscode_personalities_min_version !== undefined) {
                featureFlags.vscodePersonalitiesMinVersion =
                    resp.feature_flags.vscode_personalities_min_version;
            }
            if (resp.feature_flags.memory_classification_on_first_token !== undefined) {
                featureFlags.memoryClassificationOnFirstToken =
                    resp.feature_flags.memory_classification_on_first_token;
            }
            if (resp.feature_flags.agent_edit_tool_instructions_reminder !== undefined) {
                featureFlags.agentEditToolInstructionsReminder =
                    resp.feature_flags.agent_edit_tool_instructions_reminder;
            }
            if (resp.feature_flags.agent_edit_tool_show_result_snippet !== undefined) {
                featureFlags.agentEditToolShowResultSnippet =
                    resp.feature_flags.agent_edit_tool_show_result_snippet;
            }
            if (resp.feature_flags.agent_edit_tool_max_lines !== undefined) {
                featureFlags.agentEditToolMaxLines = resp.feature_flags.agent_edit_tool_max_lines;
            }
            if (resp.feature_flags.agent_save_file_tool_instructions_reminder !== undefined) {
                featureFlags.agentSaveFileToolInstructionsReminder =
                    resp.feature_flags.agent_save_file_tool_instructions_reminder;
            }
            if (resp.feature_flags.use_memory_snapshot_manager !== undefined) {
                featureFlags.useMemorySnapshotManager =
                    resp.feature_flags.use_memory_snapshot_manager;
            }
            if (resp.feature_flags.vscode_generate_commit_message_min_version !== undefined) {
                featureFlags.vscodeGenerateCommitMessageMinVersion =
                    resp.feature_flags.vscode_generate_commit_message_min_version;
            }
            if (resp.feature_flags.enable_rules !== undefined) {
                featureFlags.enableRules = resp.feature_flags.enable_rules;
            }
            if (resp.feature_flags.memories_text_editor_enabled !== undefined) {
                featureFlags.memoriesTextEditorEnabled =
                    resp.feature_flags.memories_text_editor_enabled;
            }
            if (resp.feature_flags.enable_prompt_enhancer !== undefined) {
                featureFlags.enablePromptEnhancer = resp.feature_flags.enable_prompt_enhancer;
            }
            if (resp.feature_flags.enable_model_registry !== undefined) {
                featureFlags.enableModelRegistry = resp.feature_flags.enable_model_registry;
            }
            if (resp.feature_flags.model_registry !== undefined) {
                try {
                    featureFlags.modelRegistry = JSON.parse(
                        resp.feature_flags.model_registry
                    ) as Record<string, string>;
                } catch (error) {
                    this._logger.error(`Parsing of "model_registry" failed.`);
                }
            }
            if (resp.feature_flags.model_info_registry !== undefined) {
                try {
                    featureFlags.modelInfoRegistry = JSON.parse(
                        resp.feature_flags.model_info_registry
                    ) as Record<string, ModelInfoRegistryEntry>;
                } catch (error) {
                    this._logger.error(`Parsing of "model_info_registry" failed.`);
                }
            }
            if (resp.feature_flags.agent_chat_model !== undefined) {
                featureFlags.agentChatModel = resp.feature_flags.agent_chat_model;
            }
            if (resp.feature_flags.vscode_task_list_min_version !== undefined) {
                featureFlags.vscodeTaskListMinVersion =
                    resp.feature_flags.vscode_task_list_min_version;
            }
            if (resp.feature_flags.vscode_support_tool_use_start_min_version !== undefined) {
                featureFlags.vscodeSupportToolUseStartMinVersion =
                    resp.feature_flags.vscode_support_tool_use_start_min_version;
            }

            if (resp.feature_flags.open_file_manager_v2_enabled !== undefined) {
                featureFlags.openFileManagerV2Enabled =
                    resp.feature_flags.open_file_manager_v2_enabled;
            }
            if (resp.feature_flags.enable_agent_auto_mode !== undefined) {
                featureFlags.enableAgentAutoMode = resp.feature_flags.enable_agent_auto_mode;
            }
            if (resp.feature_flags.enable_agent_swarm_mode !== undefined) {
                featureFlags.enableAgentSwarmMode = resp.feature_flags.enable_agent_swarm_mode;
            }
            if (resp.feature_flags.vscode_remote_agent_ssh_min_version !== undefined) {
                featureFlags.vscodeRemoteAgentSSHMinVersion =
                    resp.feature_flags.vscode_remote_agent_ssh_min_version;
            }
            if (resp.feature_flags.client_announcement !== undefined) {
                featureFlags.clientAnnouncement = resp.feature_flags.client_announcement;
            }
            if (resp.feature_flags.grep_search_tool_enable !== undefined) {
                featureFlags.grepSearchToolEnable = resp.feature_flags.grep_search_tool_enable;
            }
            if (resp.feature_flags.grep_search_tool_timelimit_sec !== undefined) {
                featureFlags.grepSearchToolTimelimitSec =
                    resp.feature_flags.grep_search_tool_timelimit_sec;
            }
            if (resp.feature_flags.grep_search_tool_output_chars_limit !== undefined) {
                featureFlags.grepSearchToolOutputCharsLimit =
                    resp.feature_flags.grep_search_tool_output_chars_limit;
            }
            if (resp.feature_flags.grep_search_tool_num_context_lines !== undefined) {
                featureFlags.grepSearchToolNumContextLines =
                    resp.feature_flags.grep_search_tool_num_context_lines;
            }
            if (resp.feature_flags.history_summary_min_version !== undefined) {
                featureFlags.historySummaryMinVersion =
                    resp.feature_flags.history_summary_min_version;
            }
            if (resp.feature_flags.history_summary_params !== undefined) {
                featureFlags.historySummaryParams = resp.feature_flags.history_summary_params;
            }
            if (resp.feature_flags.enable_new_threads_list !== undefined) {
                featureFlags.enableNewThreadsList = resp.feature_flags.enable_new_threads_list;
            }
            if (resp.feature_flags.enable_untruncated_content_storage !== undefined) {
                featureFlags.enableUntruncatedContentStorage =
                    resp.feature_flags.enable_untruncated_content_storage;
            }
            if (
                resp.feature_flags.max_lines_terminal_process_output_after_truncation !== undefined
            ) {
                featureFlags.maxLinesTerminalProcessOutputAfterTruncation =
                    resp.feature_flags.max_lines_terminal_process_output_after_truncation;
            }
            if (resp.feature_flags.max_lines_terminal_process_output !== undefined) {
                featureFlags.maxLinesTerminalProcessOutput =
                    resp.feature_flags.max_lines_terminal_process_output;
            }
            if (resp.feature_flags.truncation_footer_addition_text !== undefined) {
                featureFlags.truncationFooterAdditionText =
                    resp.feature_flags.truncation_footer_addition_text;
            }
            if (resp.feature_flags.enable_commit_indexing !== undefined) {
                featureFlags.enableCommitIndexing = resp.feature_flags.enable_commit_indexing;
            }
            if (resp.feature_flags.max_commits_to_index !== undefined) {
                featureFlags.maxCommitsToIndex = resp.feature_flags.max_commits_to_index;
            }
            if (resp.feature_flags.enable_exchange_storage !== undefined) {
                featureFlags.enableExchangeStorage = resp.feature_flags.enable_exchange_storage;
            }
            if (resp.feature_flags.enable_agent_tabs !== undefined) {
                featureFlags.enableAgentTabs = resp.feature_flags.enable_agent_tabs;
            }
            if (resp.feature_flags.enable_agent_git_tracker !== undefined) {
                featureFlags.enableAgentGitTracker = resp.feature_flags.enable_agent_git_tracker;
            }
            if (resp.feature_flags.remote_agents_resume_hint_available_ttl_days !== undefined) {
                featureFlags.remoteAgentsResumeHintAvailableTtlDays =
                    resp.feature_flags.remote_agents_resume_hint_available_ttl_days;
            }
            if (resp.feature_flags.enable_grouped_tools !== undefined) {
                featureFlags.enableGroupedTools = resp.feature_flags.enable_grouped_tools;
            }
            if (resp.feature_flags.enable_tool_use_state_storage !== undefined) {
                featureFlags.enableToolUseStateStorage =
                    resp.feature_flags.enable_tool_use_state_storage;
            }
            if (resp.feature_flags.retry_chat_stream_timeouts !== undefined) {
                featureFlags.retryChatStreamTimeouts =
                    resp.feature_flags.retry_chat_stream_timeouts;
            }
            if (resp.feature_flags.remote_agent_current_workspace !== undefined) {
                featureFlags.remoteAgentCurrentWorkspace =
                    resp.feature_flags.remote_agent_current_workspace;
            }
            if (resp.feature_flags.enable_memory_retrieval !== undefined) {
                featureFlags.enableMemoryRetrieval = resp.feature_flags.enable_memory_retrieval;
            }
            if (resp.feature_flags.vscode_min_version !== undefined) {
                featureFlags.vscodeMinVersion = resp.feature_flags.vscode_min_version;
            }
            if (resp.feature_flags.enable_swarm_mode !== undefined) {
                featureFlags.enableSwarmMode = resp.feature_flags.enable_swarm_mode;
            }
            if (resp.feature_flags.enable_parallel_tools !== undefined) {
                featureFlags.enableParallelTools = resp.feature_flags.enable_parallel_tools;
            }
            if (resp.feature_flags.enable_native_remote_mcp !== undefined) {
                featureFlags.enableNativeRemoteMcp = resp.feature_flags.enable_native_remote_mcp;
            }
            if (resp.feature_flags.enable_sentry !== undefined) {
                featureFlags.enableSentry = resp.feature_flags.enable_sentry;
            }
            if (resp.feature_flags.webview_error_sampling_rate !== undefined) {
                featureFlags.webviewErrorSamplingRate =
                    resp.feature_flags.webview_error_sampling_rate;
            }
            if (resp.feature_flags.webview_trace_sampling_rate !== undefined) {
                featureFlags.webviewTraceSamplingRate =
                    resp.feature_flags.webview_trace_sampling_rate;
            }
            if (resp.feature_flags.agent_view_tool_params !== undefined) {
                featureFlags.agentViewToolParams = resp.feature_flags.agent_view_tool_params;
            }
            if (resp.feature_flags.vscode_terminal_strategy !== undefined) {
                featureFlags.vscodeTerminalStrategy = resp.feature_flags.vscode_terminal_strategy;
            }
            if (resp.feature_flags.non_dismissible_banner_test_treatment !== undefined) {
                featureFlags.nonDismissibleBannerTestTreatment =
                    resp.feature_flags.non_dismissible_banner_test_treatment;
            }
        }

        let languages: Language[] = [];

        if (resp.languages === undefined) {
            // Temporary, until all back ends have been changed to return the set
            // of languages.
            languages = defaultSupportedLanguages;
        } else {
            verifyArray("BackGetModelsResult", "languages", resp.languages);
            languages = [];
            for (const languageInfo of resp.languages) {
                languages.push(this.toLanguage(languageInfo));
            }
        }

        const userTier: UserTier =
            (resp.user_tier?.toLowerCase().replace("_tier", "") as UserTier) ?? "unknown";

        const user = resp.user
            ? {
                  id: resp.user.id,
                  email: resp.user.email,
              }
            : undefined;

        return {
            defaultModel,
            models,
            languages,
            featureFlags,
            userTier,
            user,
        };
    }

    public async getModelConfig(): Promise<ModelConfig> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        const result = await this.callApi<ModelConfig>(
            requestId,
            config,
            "get-models",
            {},
            (json) => this.toGetModelsResult(json as BackGetModelsResult)
        );
        return result;
    }

    public async completionFeedback(feedback: CompletionFeedback): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        await this.callApi<void>(
            requestId,
            config,
            "completion-feedback",
            {
                request_id: feedback.requestId,
                rating: feedback.rating,
                note: feedback.note,
            },
            undefined
        );
    }

    public async chatFeedback(feedback: ChatFeedback): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        await this.callApi<void>(
            requestId,
            config,
            "chat-feedback",
            {
                request_id: feedback.requestId,
                rating: feedback.rating,
                note: feedback.note,
                mode: feedback.mode,
            },
            undefined
        );
    }

    public async nextEditFeedback(feedback: NextEditFeedback): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        await this.callApi<void>(
            requestId,
            config,
            "next-edit-feedback",
            {
                request_id: feedback.requestId,
                rating: feedback.rating,
                note: feedback.note,
            },
            undefined,
            config.nextEdit.url
        );
    }

    public async getAccessToken(
        authURI: string,
        tenantURL: string,
        codeVerifier: string,
        code: string
    ): Promise<string> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        const body = {
            grant_type: "authorization_code",
            client_id: config.oauth.clientID,
            code_verifier: codeVerifier,
            redirect_uri: authURI,
            code,
        };
        return await this.callApi<string>(
            requestId,
            config,
            "token",
            body,
            (json) => (json as { access_token: string })["access_token"],
            tenantURL
        );
    }

    public async uploadUserEvents(userEventBatch: any): Promise<void> {
        const requestId = this.createRequestId();
        return await this.callApi<void>(
            requestId,
            this._configListener.config,
            "record-user-events",
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            { extension_data: userEventBatch }
        );
    }

    /**
     * Convert backend notification response to frontend format
     * @returns Response containing notifications
     */
    private convertToReadNotificationsResponse(
        resp: BackReadNotificationsResponse
    ): GetNotificationsResponse {
        // Verify the response has a notifications array
        if (!Array.isArray(resp.notifications)) {
            throw new Error("Invalid notification response: notifications is not an array");
        }

        // Convert each notification from backend format to client format
        const notifications = resp.notifications.map((notification) => ({
            notificationId: notification.notification_id,
            level: this.convertNotificationLevel(notification.level),
            message: notification.message,
            actionItems: notification.action_items?.map((item) => ({
                title: item.title,
                url: item.url,
            })),
            displayType: this.convertNotificationDisplayType(notification.display_type),
        }));

        return { notifications };
    }

    private convertNotificationLevel(level: BackNotificationLevel): NotificationLevel {
        switch (level) {
            case BackNotificationLevel.NOTIFICATION_LEVEL_INFO:
                return NotificationLevel.info;
            case BackNotificationLevel.NOTIFICATION_LEVEL_WARNING:
                return NotificationLevel.warning;
            case BackNotificationLevel.NOTIFICATION_LEVEL_ERROR:
                return NotificationLevel.error;
            default:
                this._logger.warn(
                    `Unknown numeric notification level received from backend: ${String(level)}, defaulting to unspecified`
                );
                return NotificationLevel.unspecified;
        }
    }

    private convertNotificationDisplayType(
        displayType: BackNotificationDisplayType
    ): NotificationDisplayType {
        switch (displayType) {
            case BackNotificationDisplayType.DISPLAY_TYPE_TOAST:
                return NotificationDisplayType.toast;
            case BackNotificationDisplayType.DISPLAY_TYPE_BANNER:
                return NotificationDisplayType.banner;
            default:
                this._logger.warn(
                    `Unknown numeric notification display type received from backend: ${String(
                        displayType
                    )}, defaulting to unspecified`
                );
                return NotificationDisplayType.unspecified;
        }
    }

    /**
     * Read notifications for the current user
     * @returns Response containing notifications that were read
     */
    public async readNotifications(): Promise<GetNotificationsResponse> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        this._logger.debug("Reading notifications");

        const requestBody = {};

        return await this.callApi<GetNotificationsResponse>(
            requestId,
            config,
            "/notifications/read",
            requestBody,
            (json) => this.convertToReadNotificationsResponse(json as BackReadNotificationsResponse)
        );
    }

    public async markNotificationAsRead(
        notificationId: string,
        actionItemTitle?: string
    ): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        this._logger.debug("Marking notification as read");

        const requestBody = {
            notification_id: notificationId,
            action_item_title: actionItemTitle,
        };

        await this.callApi<void>(
            requestId,
            config,
            "/notifications/mark-as-read",
            requestBody,
            undefined
        );
    }

    public async clientMetrics(metrics: Array<ClientMetric>): Promise<void> {
        const config = this._configListener.config;

        // Use retry logic specifically for client-metrics to handle transient failures
        await retryWithBackoff(
            async () => {
                const requestId = this.createRequestId();
                await this.callApi<void>(
                    requestId,
                    config,
                    "client-metrics",
                    {
                        metrics,
                    },
                    undefined,
                    undefined,
                    APIServerImpl.defaultRequestTimeoutMs
                );
            },
            this._logger,
            {
                initialMS: 1000, // Start with 1 second delay
                mult: 2, // Double the delay each time
                maxMS: 30000, // Cap at 30 seconds
                maxTries: 3, // Maximum 3 attempts total
                canRetry: (e: unknown) => {
                    // Only retry on unavailable and cancelled errors for metrics
                    return (
                        APIError.isAPIErrorWithStatus(e, APIStatus.unavailable) ||
                        APIError.isAPIErrorWithStatus(e, APIStatus.cancelled)
                    );
                },
            }
        );
    }

    public async searchExternalSources(
        query: string,
        sourceTypes: string[]
    ): Promise<SearchExternalSourcesResponse> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        return await this.callApi<SearchExternalSourcesResponse>(
            requestId,
            config,
            "search-external-sources",
            {
                query,
                source_types: sourceTypes,
            },
            (json) => json as SearchExternalSourcesResponse
        );
    }

    // Given a question, returns all implicit external sources that will be
    // added to the chat context
    public async getImplicitExternalSources(
        message: string
    ): Promise<SearchExternalSourcesResponse> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        return await this.callApi<SearchExternalSourcesResponse>(
            requestId,
            config,
            "get-implicit-external-sources",
            {
                message,
            },
            (json) => json as SearchExternalSourcesResponse
        );
    }

    private convertToAgentCodebaseRetrievalResult(
        resp: BackAgentCodebaseRetrievalResult
    ): AgentCodebaseRetrievalResult {
        return {
            formattedRetrieval: resp.formatted_retrieval,
        };
    }

    public async agentCodebaseRetrieval(
        requestId: string,
        informationRequest: string,
        blobs: Blobs,
        chatHistory: Exchange[],
        maxOutputLength: number,
        options?: AgentCodebaseRetrievalOptions,
        signal?: AbortSignal
    ): Promise<AgentCodebaseRetrievalResult> {
        const config = this._configListener.config;
        return await this.callApi<AgentCodebaseRetrievalResult>(
            requestId,
            config,
            "agents/codebase-retrieval",
            {
                information_request: informationRequest,
                blobs: blobsToBlobsPayload(blobs),
                dialog: chatHistory,
                max_output_length: maxOutputLength,
                disable_codebase_retrieval: options?.disableCodebaseRetrieval ?? false,
                enable_commit_retrieval: options?.enableCommitRetrieval ?? false,
            },
            (json) =>
                this.convertToAgentCodebaseRetrievalResult(
                    json as BackAgentCodebaseRetrievalResult
                ),
            config.chat.url,
            120000,
            undefined,
            signal,
            true
        );
    }

    private convertToAgentEditFileResult(resp: BackAgentEditFileResult): AgentEditFileResult {
        return {
            modifiedFileContents: resp.modified_file_contents,
            isError: resp.is_error,
        };
    }

    public async agentEditFile(
        requestId: string,
        filePath: string,
        editSummary: string,
        detailedEditDescription: string,
        fileContents: string,
        signal?: AbortSignal
    ): Promise<AgentEditFileResult> {
        const config = this._configListener.config;
        return await this.callApi<AgentEditFileResult>(
            requestId,
            config,
            "agents/edit-file",
            {
                file_path: filePath,
                edit_summary: editSummary,
                detailed_edit_description: detailedEditDescription,
                file_contents: fileContents,
            },
            (json) => this.convertToAgentEditFileResult(json as BackAgentEditFileResult),
            config.chat.url,
            120000,
            undefined,
            signal
        );
    }

    private convertToToolSafety(toolSafety: BackToolSafety): ToolSafety {
        switch (toolSafety) {
            case BackToolSafety.Unsafe:
                return ToolSafety.Unsafe;
            case BackToolSafety.Safe:
                return ToolSafety.Safe;
            case BackToolSafety.Check:
                return ToolSafety.Check;
            default:
                return ToolSafety.Unsafe;
        }
    }

    private convertToListRemoteToolsResult(resp: BackListRemoteToolsResult): ListRemoteToolsResult {
        return {
            tools: resp.tools.map((tool) => ({
                toolDefinition: tool.tool_definition,
                remoteToolId: tool.remote_tool_id,
                availabilityStatus: tool.availability_status,
                toolSafety: this.convertToToolSafety(tool.tool_safety),
                oauthUrl: tool.oauth_url,
            })),
        };
    }

    public async listRemoteTools(toolIDs: RemoteToolId[]): Promise<ListRemoteToolsResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;

        return await this.callApi<ListRemoteToolsResult>(
            requestId,
            config,
            "agents/list-remote-tools",
            {
                tool_id_list: {
                    tool_ids: toolIDs,
                },
            },
            (json) => this.convertToListRemoteToolsResult(json as BackListRemoteToolsResult),
            config.chat.url,
            120000
        );
    }

    private convertToCheckToolSafetyResult(resp: BackCheckToolSafetyResult): boolean {
        return resp.is_safe;
    }

    public async checkToolSafety(toolId: RemoteToolId, toolInputJson: string): Promise<boolean> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<boolean>(
            requestId,
            config,
            "agents/check-tool-safety",
            {
                tool_id: toolId,
                tool_input_json: toolInputJson,
            },
            (json) => this.convertToCheckToolSafetyResult(json as BackCheckToolSafetyResult),
            config.chat.url,
            120000
        );
    }

    private convertToRunRemoteToolResult(resp: BackRunRemoteToolResult): RunRemoteToolResult {
        return {
            toolOutput: resp.tool_output,
            toolResultMessage: resp.tool_result_message,
            status: resp.status,
        };
    }

    public async runRemoteTool(
        requestId: string,
        toolName: string,
        toolInputJson: string,
        toolId: RemoteToolId,
        extraToolInput: ExtraToolInput | undefined,
        signal: AbortSignal
    ): Promise<RunRemoteToolResult> {
        const config = this._configListener.config;
        let extraToolInputJson: Record<string, any> = {};
        if (extraToolInput) {
            if (toolId === RemoteToolId.Jira || toolId === RemoteToolId.Confluence) {
                const atlassianConfig = extraToolInput as AtlassianToolExtraInput;
                extraToolInputJson = {
                    extra_tool_input: {
                        atlassian_tool_extra_input: {
                            server_url: atlassianConfig.serverUrl,
                            personal_api_token: atlassianConfig.personalApiToken,
                            username: atlassianConfig.username,
                        },
                    },
                };
            } else if (toolId === RemoteToolId.Notion) {
                const notionConfig = extraToolInput as NotionToolExtraInput;
                extraToolInputJson = {
                    extra_tool_input: {
                        notion_tool_extra_input: {
                            api_token: notionConfig.apiToken,
                        },
                    },
                };
            } else if (toolId === RemoteToolId.Linear) {
                const linearConfig = extraToolInput as LinearToolExtraInput;
                extraToolInputJson = {
                    extra_tool_input: {
                        linear_tool_extra_input: {
                            api_token: linearConfig.apiToken,
                        },
                    },
                };
            } else if (toolId === RemoteToolId.GitHubApi) {
                const githubConfig = extraToolInput as GitHubToolExtraInput;
                extraToolInputJson = {
                    extra_tool_input: {
                        github_tool_extra_input: {
                            api_token: githubConfig.apiToken,
                        },
                    },
                };
            }
        }
        return await this.callApi<RunRemoteToolResult>(
            requestId,
            config,
            "agents/run-remote-tool",
            {
                tool_name: toolName,
                tool_input_json: toolInputJson,
                tool_id: toolId,
                ...extraToolInputJson,
            },
            (json) => this.convertToRunRemoteToolResult(json as BackRunRemoteToolResult),
            config.chat.url,
            120000,
            undefined,
            signal
        );
    }

    private convertToRevokeToolAccessResult(
        resp: BackRevokeToolAccessResult
    ): RevokeToolAccessResult {
        return {
            status: resp.status,
        };
    }

    public async revokeToolAccess(toolId: RemoteToolId): Promise<RevokeToolAccessResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<RevokeToolAccessResult>(
            requestId,
            config,
            "agents/revoke-tool-access",
            {
                tool_id: toolId,
            },
            (json) => this.convertToRevokeToolAccessResult(json as BackRevokeToolAccessResult),
            config.chat.url
        );
    }

    public reportError(
        _originalRequestId: string | null,
        _sanitizedMessage: string,
        _stackTrace: string,
        _diagnostics: KeyValuePair[]
    ): Promise<void> {
        return Promise.reject(
            new Error("reportError should only be used via APIServerImplWithErrorReporting")
        );
    }

    public async reportClientCompletionTimelines(
        timelines: ClientCompletionTimline[]
    ): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        await this.callApi<void>(
            requestId,
            config,
            "/client-completion-timelines",
            {
                timelines,
            },
            undefined,
            undefined,
            APIServerImpl.defaultRequestTimeoutMs
        );
    }

    public async saveChat(
        conversation_id: string,
        conversation: Exchange[],
        title: string
    ): Promise<SaveChatResult> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();

        const payload = {
            conversation_id: conversation_id,
            chat: conversation.map((exchange) => ({
                request_message: exchange.request_message,
                response_text: exchange.response_text,
                request_id: exchange.request_id,
            })),
            title: title,
        };

        const result = await this.callApi<SaveChatResult>(
            requestId,
            config,
            "/save-chat",
            payload,
            (json) => json as SaveChatResult,
            config.chat.url,
            APIServerImpl.defaultRequestTimeoutMs
        );
        return result;
    }

    public async upsertUserSecret(
        requestId: string,
        name: string,
        value: string,
        tags?: Record<string, string>,
        description?: string
    ): Promise<UpsertUserSecretResult> {
        const config = this._configListener.config;
        const payload = {
            name,
            value,
            tags: tags || {},
            description: description || "",
        };

        return await this.callApi<UpsertUserSecretResult>(
            requestId,
            config,
            "/user-secrets/upsert",
            payload,
            (json) => json as UpsertUserSecretResult
        );
    }

    public async listUserSecrets(
        requestId: string,
        includeValues?: boolean,
        namePattern?: string
    ): Promise<ListUserSecretsResult> {
        const config = this._configListener.config;
        const payload = {
            include_values: includeValues || false,
            name_pattern: namePattern || "",
        };

        return await this.callApi<ListUserSecretsResult>(
            requestId,
            config,
            "/user-secrets/list",
            payload,
            (json) => json as ListUserSecretsResult
        );
    }

    public async deleteUserSecret(
        requestId: string,
        name: string
    ): Promise<DeleteUserSecretResult> {
        const config = this._configListener.config;
        const payload = {
            name,
        };

        return await this.callApi<DeleteUserSecretResult>(
            requestId,
            config,
            "/user-secrets/delete",
            payload,
            (json) => json as DeleteUserSecretResult
        );
    }
}

export class APIServerImplWithErrorReporting extends APIServerImpl {
    constructor(
        configListener: AugmentConfigListener,
        auth: AuthSessionStore,
        sessionId: string,
        userAgent: string,
        fetchFunction: FetchFunction
    ) {
        super(configListener, auth, sessionId, userAgent, fetchFunction);
    }

    // Wrapper function for callApi that adds some automated error reporting using implementation
    // details about what types of errors callApi is likely to throw and what they indicate.
    protected async callApi<T extends {} | void>(
        requestId: string,
        config: AugmentConfig,
        apiEndpoint: string,
        body: Record<string, any>,
        convert: (json: {}) => T = (json) => json as T,
        baseURL?: string,
        requestTimeoutMs?: number,
        apiTiming?: APITiming,
        signal?: AbortSignal,
        dropAndReplaceSurrogates: boolean = false
    ): Promise<T> {
        const startTime = Date.now();
        try {
            return await super.callApi<T>(
                requestId,
                config,
                apiEndpoint,
                body,
                convert,
                baseURL,
                requestTimeoutMs,
                apiTiming,
                signal,
                dropAndReplaceSurrogates
            );
        } catch (e) {
            await this.handleError(e, apiEndpoint, body, baseURL ?? "", requestId, startTime);
            throw e;
        }
    }

    async handleError(
        e: any,
        apiEndpoint: string,
        body: Record<string, any>,
        baseURL: string,
        requestId: string,
        startTime: number
    ) {
        // Don't bother reporting telemetry for cancelled requests
        if (APIError.isAPIErrorWithStatus(e, APIStatus.cancelled)) {
            throw e;
        }

        const bodyLength = safeJsonStringify(body, this._logger).length;

        const diagnostics = [
            {
                key: "body_length",
                value: `${bodyLength}`,
            },
            {
                key: "start_time",
                value: `${startTime}`,
            },
            {
                key: "end_time",
                value: `${Date.now()}`,
            },
            {
                key: "message",
                value: getErrmsg(e),
            },
        ];
        const stackTrace = e instanceof Error ? e.stack : undefined;
        if (e instanceof APIError && e.status === APIStatus.augmentTooLarge) {
            diagnostics.push({
                key: "object_size_breakdown",
                value: `${getPropertySizes(body)}`,
            });
        }
        const errorStatus = e instanceof APIError ? e.status : APIStatus.unknown;
        let sanitizedMessage = `${apiEndpoint} call failed with APIStatus ${APIStatus[errorStatus]}`;
        if (e instanceof ConversionFailure) {
            sanitizedMessage = `converting ${apiEndpoint} response failed`;
        }
        if (baseURL && this.getUniqueExtraURLs().has(baseURL)) {
            this._logger.error(`API error ${apiEndpoint} to ${baseURL}: ${sanitizedMessage}`);
        } else {
            await this.reportError(requestId, sanitizedMessage, stackTrace ?? "", diagnostics);
        }
        throw e;
    }

    // Wrapper function for callApiStream that adds some automated error reporting using implementation
    // details about what types of errors callApi is likely to throw and what they indicate.
    protected async callApiStream<T extends any>(
        requestId: string,
        config: AugmentConfig,
        apiEndpoint: string,
        body: Record<string, any>,
        convert?: (json: any) => T,
        baseURL?: string,
        requestTimeoutMs?: number,
        sessionId?: string,
        signal?: AbortSignal,
        dropAndReplaceSurrogates: boolean = false
    ): Promise<AsyncIterable<T>> {
        const startTime = Date.now();
        try {
            return await super.callApiStream<T>(
                requestId,
                config,
                apiEndpoint,
                body,
                convert,
                baseURL,
                requestTimeoutMs,
                sessionId,
                signal,
                dropAndReplaceSurrogates
            );
        } catch (e) {
            await this.handleError(e, apiEndpoint, body, baseURL ?? "", requestId, startTime);
            throw e;
        }
    }
    /**
     * Report an error to the Augment backend for tracking in logs and Request Insight.
     *
     * @param originalRequestId The original request ID, if any.
     * @param sanitizedMessage The sanitized error message (MUST NOT CONTAIN user metadata)
     * @param stackTrace The stack trace of the error
     * @param diagnostics Additional unstructured diagnostic information (user metadata goes here)
     */
    public async reportError(
        originalRequestId: string | null,
        sanitizedMessage: string,
        stackTrace: string,
        diagnostics: KeyValuePair[]
    ): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        // Remove content of the form " (/...)" from the stack trace
        // since the absolute paths could be leaking user directory names
        const sanitizedStackTrace = stackTrace.replace(/ \(\/[^()]+\)/g, "");
        try {
            return await super.callApi<void>(
                requestId,
                config,
                "report-error",
                {
                    original_request_id: originalRequestId,
                    sanitized_message: sanitizedMessage,
                    stack_trace: sanitizedStackTrace,
                    diagnostics,
                },
                undefined,
                undefined,
                500 // 0.5 second timeout: we don't want error telemetry to impact responsiveness
            );
        } catch (e) {
            // Stifle errors from the API call to avoid stomping on useful error information
            // from the original error or delaying more than expected due to retries.
            this._logger.error(
                `Dropping error report "${sanitizedMessage}" due to error: ${getErrmsg(e)}`
            );
        }
    }
}

class SequenceID {
    private _sequenceId = 0;

    public next(): number {
        return this._sequenceId++;
    }
}
