package com.augmentcode.intellij.featureflags

import com.augmentcode.api.EloModelConfiguration

const val DEFAULT_MAX_UPLOAD_SIZE_BYTES = 128 * 1024
const val DEFAULT_SMALL_SYNC_THRESHOLD = 15
const val DEFAULT_BIG_SYNC_THRESHOLD = 1000
const val DEFAULT_USER_GUIDELINES_LENGTH_LIMIT = 2000
const val DEFAULT_WORKSPACE_GUIDELINES_LENGTH_LIMIT = 2000

val DefaultFeatureFlags =
  FeatureFlags(
    chatMultimodalEnabled = false,
    enableCompletionsHistory = false,
    agentModeEnabled = false,
    enableRules = false,
    promptEnhancerEnabled = false,
    enableChatMermaidDiagrams = false,
    enableDesignSystemRichTextEditor = false,
    smartPastePrecomputeMode = "visible-hover",
    enableSmartPaste = false,
    useNewThreadsMenu = false,
    enableNewThreadsList = false,
    enableExternalSourcesInChat = false,
    shareServiceEnabled = false,
    enableHomespunGitignore = false,
    memoriesParams = "{}",
    userGuidelinesLengthLimit = DEFAULT_USER_GUIDELINES_LENGTH_LIMIT,
    workspaceGuidelinesLengthLimit = DEFAULT_WORKSPACE_GUIDELINES_LENGTH_LIMIT,
    chatWithToolsEnabled = false,
    sentryEnabled = false,
    webviewErrorSamplingRate = 0.0,
    pluginErrorSamplingRate = 0.0,
    webviewTraceSamplingRate = 0.0,
    pluginTraceSamplingRate = 0.0,
    maxUploadSizeBytes = DEFAULT_MAX_UPLOAD_SIZE_BYTES,
    bypassLanguageFilter = false,
    additionalChatModels = emptyMap(),
    enableAgentAutoMode = false,
    preferenceCollectionAllowed = false,
    eloModelConfiguration =
      EloModelConfiguration().apply {
        highPriorityModels = emptyList()
        regularBattleModels = emptyList()
        highPriorityThreshold = 0.5
      },
    enableEdtFreezeDetection = false,
    indexingV3Enabled = false,
    enableWebviewPerformanceMonitoring = false,
    taskListEnabled = false,
    enableExchangeStorage = false,
    enableToolUseStateStorage = false,
    useHistorySummary = false,
    historySummaryParams = "",
    agentEditTool = "backend_edit_tool",
    agentEditToolMinViewSize = 0,
    agentEditToolSchemaType = "StrReplaceEditorToolDefinitionNested",
    agentEditToolEnableFuzzyMatching = false,
    agentEditToolFuzzyMatchSuccessMessage =
      "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
    agentEditToolFuzzyMatchMaxDiff = 50,
    agentEditToolFuzzyMatchMaxDiffRatio = 0.15,
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs = 5,
    agentEditToolInstructionsReminder = false,
    agentEditToolShowResultSnippet = true,
    agentEditToolMaxLines = 200,
    agentSaveFileToolInstructionsReminder = false,
    grepSearchToolEnable = false,
    grepSearchToolTimelimitSec = 10,
    grepSearchToolOutputCharsLimit = 5000,
    grepSearchToolNumContextLines = 5,
    retryChatStreamTimeouts = false,
    enableAgentTabs = false,
    enableAgentGitTracker = false,
    maxTrackableFileCount = null,
    maxTrackableFileCountWithoutPermission = null,
    enableMemoryRetrieval = false,
    enableModelRegistry = false,
    modelRegistry = emptyMap(),
    agentChatModel = null,
    agentViewToolParams = "{}",
    nonDismissibleBannerTestTreatment = "off",
  )
