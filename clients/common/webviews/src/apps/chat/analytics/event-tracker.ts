import { get, type Readable } from "svelte/store";
import type { IExtensionClient } from "../extension-client";
import { type SendMode } from "../types/send-mode";
import { type ChatModeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type AgentExecutionMode } from "../models/chat-model";
import { type AnalyticsEventName, type EventNameToPropertiesMap, type ExperimentName, type ExperimentTreatment } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";


export interface EventTrackerDependencies {
  extensionClient: IExtensionClient;
  chatModeType: Readable<ChatModeType>;
  agentExecutionMode: Readable<AgentExecutionMode>;
  currentSendMode: Readable<SendMode>;
}

interface ChatMessagePayload {
  chatMode: ChatModeType;
  agentExecutionMode?: AgentExecutionMode;
  sendMode?: SendMode;
}

/**
 * Centralized analytics event tracking for the chat application.
 * Provides typed methods for tracking various user interactions and system events.
 */
export class EventTracker {
  constructor(private dependencies: EventTrackerDependencies) {}

  private _getChatMessagePayload(): ChatMessagePayload {
    const chatMode = get(this.dependencies.chatModeType);
    const agentExecutionMode = get(this.dependencies.agentExecutionMode);

    const payload: ChatMessagePayload = {
      chatMode,
      agentExecutionMode: chatMode === "localAgent" ? agentExecutionMode : undefined,
    };
    if (chatMode === "localAgent") {
      payload.sendMode = get(this.dependencies.currentSendMode);
    }

    return payload;
  }

  /**
   * Generic method to track events that include chat message payload
   * The type T is automatically inferred based on the eventName parameter
   */
  trackEvent<K extends AnalyticsEventName>(
    eventName: K,
    payload: Omit<EventNameToPropertiesMap[K], keyof ChatMessagePayload>,
  ): void {
    const eventProperties = {
      ...this._getChatMessagePayload(),
      ...payload,
    } as EventNameToPropertiesMap[K];

    this.trackSimpleEvent(eventName, eventProperties);
  }

  /**
   * Track events without chat payload (for events that don't need chat context)
   */
  trackSimpleEvent<K extends AnalyticsEventName>(
    eventName: K,
    properties: EventNameToPropertiesMap[K],
  ): void {
    this.dependencies.extensionClient.trackEventWithTypes(eventName, properties);
  }

  /**
   * Track experiment viewed events
   */
  trackExperimentViewed<K extends AnalyticsEventName>(
    experimentName: ExperimentName,
    treatment: ExperimentTreatment,
    properties?: EventNameToPropertiesMap[K],
  ): void {
    this.dependencies.extensionClient.trackExperimentViewed(experimentName, treatment, properties);
  }
}
