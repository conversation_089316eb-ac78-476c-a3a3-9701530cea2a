import { type MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import {
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { ChatMode, type ChatModeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { get, derived, type Readable } from "svelte/store";
import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { getModeDisplayInfo } from "../components/buttons/mode-display-helper";
import { NEW_AGENT_KEY } from "./agent-constants";
import { type AgentConversationModel } from "./agent-conversation-model";
import { AgentExecutionMode, type ChatModel } from "./chat-model";
import {
  ModeSelectorAction,
  ModeSelectorMode,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";

/**
 * Helper functions to work with mode states using existing enums
 * This avoids creating a duplicate enum and reuses ChatMode + AgentExecutionMode
 */
type ModeState = {
  chatMode: ChatMode;
  agentExecution?: AgentExecutionMode;
};

const createModeState = (chatMode: ChatMode, agentExecution?: AgentExecutionMode): ModeState => ({
  chatMode,
  agentExecution,
});

const CHAT_STATE = createModeState(ChatMode.chat);
const AGENT_MANUAL_STATE = createModeState(ChatMode.agent, AgentExecutionMode.manual);
const AGENT_AUTO_STATE = createModeState(ChatMode.agent, AgentExecutionMode.auto);
const REMOTE_AGENT_STATE = createModeState(ChatMode.remoteAgent);

export class ChatModeModel implements MessageConsumer {
  static key = "chatModeModel";

  private _prevRemoteAgentWindowOpts: {
    remoteAgentId: string | undefined;
    isRemoteAgentSshWindow: boolean;
  } = {
    remoteAgentId: undefined,
    isRemoteAgentSshWindow: false,
  };

  private _chatModeType: Readable<ChatModeType>;

  constructor(
    private readonly _chatModel: ChatModel,
    private readonly _agentConversationModel: AgentConversationModel,
    private readonly _remoteAgentsModel: RemoteAgentsModel,
  ) {
    // Initialize the derived store for chat mode type
    this._chatModeType = derived(
      [
        this._agentConversationModel.isCurrConversationAgentic,
        this._chatModel.agentExecutionMode,
        this._remoteAgentsModel,
      ],
      ([isCurrConversationAgentic, agentExecutionMode, remoteAgentsModel]) => {
        const modeDisplayInfo = getModeDisplayInfo({
          isConversationAgentic: isCurrConversationAgentic,
          agentExecutionMode: agentExecutionMode,
          isBackgroundAgent: remoteAgentsModel?.isActive || false,
        });
        return modeDisplayInfo.type;
      },
    );

    // Subscribe to isRemoteAgentSshWindow changes to handle the remote agent window state
    // If that state didn't change, do nothing
    this._remoteAgentsModel.subscribe((remoteAgentsModel) => {
      if (
        remoteAgentsModel.isRemoteAgentSshWindow !==
          this._prevRemoteAgentWindowOpts.isRemoteAgentSshWindow ||
        remoteAgentsModel.remoteAgentSshWindowId !== this._prevRemoteAgentWindowOpts.remoteAgentId
      ) {
        this._prevRemoteAgentWindowOpts = {
          remoteAgentId: remoteAgentsModel.remoteAgentSshWindowId,
          isRemoteAgentSshWindow: remoteAgentsModel.isRemoteAgentSshWindow,
        };
        this.handleisRemoteAgentSshWindow(remoteAgentsModel);
      }
    });
  }

  private handleisRemoteAgentSshWindow(remoteAgentsModel: RemoteAgentsModel) {
    if (remoteAgentsModel.isRemoteAgentSshWindow) {
      this.setToRemoteAgent(remoteAgentsModel.remoteAgentSshWindowId);
    }
  }

  /**
   * Maps current state to ModeSelectorMode
   */
  private getCurrentModeSelectorMode(): ModeSelectorMode {
    const isAgentic = get(this._agentConversationModel.isCurrConversationAgentic);
    const isRemoteAgentActive = this._remoteAgentsModel.isActive;

    if (isRemoteAgentActive) {
      return ModeSelectorMode.remoteAgent;
    } else if (isAgentic) {
      return ModeSelectorMode.agent;
    } else {
      return ModeSelectorMode.chat;
    }
  }

  /**
   * Reports a mode selector event via the RemoteAgentsModel using the new abstraction
   */
  async reportModeSelectorEvent(action: ModeSelectorAction, selectedMode?: ModeSelectorMode) {
    try {
      const currentMode = selectedMode || this.getCurrentModeSelectorMode();
      await this._remoteAgentsModel.reportChatModeEvent(action, currentMode);
    } catch (error) {
      // Don't let error reporting itself cause issues
      console.error("Failed to report chat mode event:", error);
    }
  }

  /**
   * Reports a mode selector open event
   */
  reportModeSelectorOpen() {
    this.reportModeSelectorEvent(ModeSelectorAction.open);
  }

  /**
   * Reports a mode selector close event
   */
  reportModeSelectorClose() {
    this.reportModeSelectorEvent(ModeSelectorAction.close);
  }

  /**
   * Reports a mode selector init event with the current mode
   */
  reportModeSelectorInit() {
    this.reportModeSelectorEvent(ModeSelectorAction.init);
  }

  /**
   * Toggles between chat and agent modes in a cycle:
   * chat -> agent (manual) -> agent (auto) -> remote agent (if available) -> chat
   *
   * @param reverse If true, toggle in reverse direction
   */
  async cycleChatAgentModes(reverse: boolean = false): Promise<void> {
    const currentState = this.getCurrentModeState();
    const nextState = this.getNextModeInCycle(currentState, !reverse);
    await this.transitionToMode(nextState);
  }

  async toggleChatAgentAutoMode(source: "button" | "keybinding"): Promise<void> {
    if (!this.isInAgentState()) {
      return;
    }
    const currentAgentAutoMode = get(this._chatModel.agentExecutionMode);
    const newMode =
      currentAgentAutoMode === AgentExecutionMode.auto
        ? AgentExecutionMode.manual
        : AgentExecutionMode.auto;
    this._chatModel.agentExecutionMode.set(newMode);

    this._chatModel.eventTracker?.trackSimpleEvent(ANALYTICS_EVENTS.AGENT_EXECUTION_MODE_TOGGLED, {
      source,
      newMode,
    });
  }

  isInAgentState() {
    const currentState = this.getCurrentModeState();
    return currentState === AGENT_AUTO_STATE || currentState === AGENT_MANUAL_STATE;
  }

  /**
   * Get the current mode state as a simple enum
   */
  private getCurrentModeState(): ModeState {
    const isAgentic = get(this._agentConversationModel.isCurrConversationAgentic);
    const currentMode = get(this._chatModel.agentExecutionMode);
    const isRemoteAgentActive = this._remoteAgentsModel.isActive;

    if (isRemoteAgentActive) return REMOTE_AGENT_STATE;
    if (isAgentic && currentMode === AgentExecutionMode.auto) return AGENT_AUTO_STATE;
    if (isAgentic && currentMode === AgentExecutionMode.manual) return AGENT_MANUAL_STATE;
    return CHAT_STATE;
  }

  /**
   * Get the next mode in the cycle based on current state and direction
   */
  private getNextModeInCycle(currentState: ModeState, forward: boolean): ModeState {
    const enableAgentAutoMode = this._chatModel.flags.enableAgentAutoMode;
    const remoteAgentsEnabled = this._chatModel.flags.enableBackgroundAgents;

    // Define the full cycle order using our constants
    const fullCycle = [
      CHAT_STATE,
      AGENT_MANUAL_STATE,
      ...(enableAgentAutoMode ? [AGENT_AUTO_STATE] : []),
      ...(remoteAgentsEnabled ? [REMOTE_AGENT_STATE] : []),
    ];

    const currentIndex = fullCycle.findIndex(
      (state) =>
        state.chatMode === currentState.chatMode &&
        state.agentExecution === currentState.agentExecution,
    );
    if (currentIndex === -1) return CHAT_STATE; // Fallback

    let nextIndex;
    if (forward) {
      nextIndex = (currentIndex + 1) % fullCycle.length;
    } else {
      nextIndex = (currentIndex - 1 + fullCycle.length) % fullCycle.length;
    }

    return fullCycle[nextIndex];
  }

  /**
   * Transition to the specified mode state
   */
  private async transitionToMode(state: ModeState): Promise<void> {
    try {
      // Use the chatMode property to determine the transition
      switch (state.chatMode) {
        case ChatMode.chat:
          this.handleSetToThreadType("chat");
          break;
        case ChatMode.agent:
          // Use agentExecution to determine manual vs auto
          await this.handleSetToThreadType(
            "localAgent",
            state.agentExecution || AgentExecutionMode.manual,
          );
          break;
        case ChatMode.remoteAgent:
          this.handleSetToThreadType("remoteAgent");
          break;
        default:
          console.warn(`Unknown chat mode: ${state.chatMode}, falling back to chat mode`);
          this.handleSetToThreadType("chat");
          break;
      }
    } catch (error) {
      console.error(`Failed to transition to mode ${JSON.stringify(state)}:`, error);
      // Fallback to chat mode on any transition failure
      this.handleSetToThreadType("chat");
    }
  }

  get isRemoteAgentSshWindow() {
    return this._remoteAgentsModel.isRemoteAgentSshWindow;
  }

  get remoteAgentSshWindowId() {
    return this._remoteAgentsModel.remoteAgentSshWindowId;
  }

  get isRemoteAgentActive() {
    return this._remoteAgentsModel.isActive;
  }

  private async setToLocal(mode?: AgentExecutionMode) {
    // Don't allow changing modes in a remote agent window
    if (this.isRemoteAgentSshWindow) {
      return;
    }

    // Check if dependencies are properly initialized
    if (!this._agentConversationModel) {
      console.error("AgentConversationModel is not initialized");
      return;
    }

    this._remoteAgentsModel.setIsActive(false);
    if (mode) {
      this._chatModel.agentExecutionMode.set(mode);
    }

    this._chatModel.currentConversationModel.resetTotalCharactersCache();
  }

  public async setToRemoteAgent(agentId?: string | null) {
    // Check if dependencies are properly initialized
    if (!this._agentConversationModel) {
      console.error("AgentConversationModel is not initialized");
      return;
    }

    // In a remote agent window, only allow setting to the specific remote agent
    if (this.isRemoteAgentSshWindow) {
      // If an agent ID is provided, make sure it matches the remote agent ID
      if (agentId && this.remoteAgentSshWindowId && agentId !== this.remoteAgentSshWindowId) {
        agentId = this.remoteAgentSshWindowId;
      }

      // If no agent ID is provided, use the remote agent ID
      if (!agentId && this.remoteAgentSshWindowId) {
        agentId = this.remoteAgentSshWindowId;
      }
    }

    this._remoteAgentsModel.setIsActive(true);

    // If we're creating a new agent, use the static key
    if (agentId) {
      this._remoteAgentsModel.setCurrentAgent(agentId);
    } else if (agentId === null) {
      this._remoteAgentsModel.clearCurrentAgent();
    } else if (!this._remoteAgentsModel?.currentAgent) {
      // The agents list is sorted by start time, so the first one is the most recent
      const mostRecentAgent =
        this._remoteAgentsModel.agentOverviews.length > 0
          ? this._remoteAgentsModel.agentOverviews[0]
          : undefined;
      if (mostRecentAgent) {
        this._remoteAgentsModel.setCurrentAgent(mostRecentAgent.remote_agent_id);
      }
    }
    this._chatModel.currentConversationModel.resetTotalCharactersCache();
  }

  private _setModelIdOnNewConversation(currentModelId: string | null | undefined) {
    // If the feature flag is off, don't inherit the model ID
    if (!this._chatModel.flags.enableModelRegistry) {
      return;
    }

    // Inherit the model ID of current conversation if it's set
    if (currentModelId !== undefined) {
      this._chatModel.currentConversationModel.setSelectedModelId(currentModelId);
    }
  }

  /**
   * Shared function to create or use an existing thread
   * This ensures we only have one stub thread regardless of the agent type selected
   */
  public async createNewThread(type: ChatModeType, mode?: AgentExecutionMode) {
    const currentModelId = this._chatModel.currentConversationModel.selectedModelId;

    // Set the appropriate mode
    if (type === "chat") {
      await this._chatModel.setCurrentConversation(undefined, true, {
        noopIfSameConversation: true,
      });
      await this.setToLocal(mode);
      // Set this metadata to be "chat" type, only set this for new threads
      this._chatModel.currentConversationModel.extraData = {
        ...this._chatModel.currentConversationModel.extraData,
        isAgentConversation: false,
        baselineTimestamp: 0,
      };
    } else if (type === "localAgent") {
      await this._chatModel.setCurrentConversation(undefined, true, {
        noopIfSameConversation: true,
      });
      await this.setToLocal(mode);

      // Set this metadata to be "agent" type, only set this for new threads
      this._chatModel.currentConversationModel.extraData = {
        ...this._chatModel.currentConversationModel.extraData,
        isAgentConversation: true,
        baselineTimestamp: 0,
      };
      this._setModelIdOnNewConversation(currentModelId);
    } else if (type === "remoteAgent") {
      this._remoteAgentsModel.setIsActive(true);
      this.setToRemoteAgent(null);
      this._setModelIdOnNewConversation(currentModelId);
    }
  }

  get chatModeType(): Readable<ChatModeType> {
    return this._chatModeType;
  }

  get getAgentExecutionMode(): Readable<AgentExecutionMode> {
    return this._chatModel.agentExecutionMode;
  }

  /**
   * Creates a thread of the current type based on the current mode info
   * This replicates the logic from NewThreadDropdown.svelte
   */
  public async createThreadOfCurrentType() {
    // Get current mode info with the same logic as NewThreadDropdown
    const agentExecutionMode = get(this._chatModel.agentExecutionMode);
    const chatModeType = get(this.chatModeType);

    if (chatModeType === "remoteAgent") {
      // Remote agent mode
      await this.createNewThread("remoteAgent");
    } else if (chatModeType === "localAgent") {
      // Local agent mode - determine execution mode
      const currentAgentMode = agentExecutionMode;
      await this.createNewThread("localAgent", currentAgentMode);
    } else if (chatModeType === "chat") {
      // Chat mode
      await this.createNewThread("chat");
    } else {
      // Shouldn't happen fallback
      await this.createNewThread("chat");
    }
  }

  /**
   * Find the latest thread of the specified type
   */
  private findLatestThread(threadType: "chat" | "localAgent" | "remoteAgent"): string | undefined {
    const orderedChatConversations = this._chatModel.orderedConversations();
    switch (threadType) {
      case "chat":
        return orderedChatConversations.find(
          (conv) => !conv.extraData?.isAgentConversation && conv.id !== NEW_AGENT_KEY,
        )?.id;
      case "localAgent":
        return orderedChatConversations.find(
          (conv) =>
            conv.extraData?.isAgentConversation === true &&
            !conv.extraData?.isRemoteAgentConversation &&
            conv.id !== NEW_AGENT_KEY,
        )?.id;
      case "remoteAgent": {
        if (this._remoteAgentsModel.currentAgent) {
          return this._remoteAgentsModel.currentAgent.remote_agent_id;
        }

        const remoteAgents = this._remoteAgentsModel.agentOverviews || [];
        return remoteAgents.length > 0 ? remoteAgents[0].remote_agent_id : undefined;
      }
      default:
        return undefined;
    }
  }

  public handleSetToChat() {
    this.handleSetToThreadType("chat");
  }

  public handleSetToAgent(mode?: AgentExecutionMode) {
    this.handleSetToThreadType("localAgent", mode);
  }

  public handleSetToBackgroundAgent() {
    this.handleSetToThreadType("remoteAgent");
  }

  /**
   * Handles switching to the specified thread type, preferring the latest existing thread of that type.
   * Only creates a new thread if no existing thread of the requested type exists.
   *
   * @param threadType The type of thread to switch to ('chat', 'localAgent', or 'remoteAgent')
   * @param mode The agent execution mode to use (only applicable for local agents)
   */
  async handleSetToThreadType(threadType: ChatModeType, mode?: AgentExecutionMode) {
    // Special validation for remote agent mode
    if (threadType === "remoteAgent" && !this._remoteAgentsModel) {
      console.error("No remote agents model found");
      return;
    }

    const latestThreadId = this.findLatestThread(threadType);

    if (!latestThreadId) {
      // Create new thread based on type - only when no existing thread of this type exists
      // Get the current selectedModelId to pass to the new thread
      switch (threadType) {
        case "chat":
          await this.createNewThread("chat");
          break;
        case "localAgent":
          await this.createNewThread("localAgent", mode);
          break;
        case "remoteAgent":
          await this.createNewThread("remoteAgent");
          break;
      }
    } else {
      // Switch to existing thread
      this.switchToThread(threadType, latestThreadId, mode);
    }
  }

  /**
   * Switches to a thread based on its type and ID.
   * This is a convenience method that handles switching between chat, local agent, and remote agent modes.
   *
   * @param threadType The type of thread to switch to ('chat', 'localAgent', or 'remoteAgent')
   * @param threadId The ID of the thread to switch to
   * @param agentExecutionMode The agent execution mode to use (only applicable for local agents)
   * @returns A boolean indicating whether the switch was successful
   */
  public switchToThread(
    threadType: "chat" | "localAgent" | "remoteAgent",
    threadId: string,
    agentExecutionMode?: AgentExecutionMode,
  ): boolean {
    // Don't allow changing modes in a remote agent window unless it's to the current remote agent
    if (
      this.isRemoteAgentSshWindow &&
      (threadType !== "remoteAgent" ||
        (this.remoteAgentSshWindowId && threadId !== this.remoteAgentSshWindowId))
    ) {
      return false;
    }

    // Handle different thread types
    if (threadType === "remoteAgent") {
      // For remote agents, set the current agent and switch to remote agent mode
      if (threadId === NEW_AGENT_KEY) {
        this.setToRemoteAgent(null);
      } else {
        this.setToRemoteAgent(threadId);
      }
    } else {
      // For chat and local agent threads, first set the current conversation
      void this._chatModel
        .setCurrentConversation(threadId, true, { noopIfSameConversation: true })
        .then(async () => {
          this.setToLocal(agentExecutionMode);
        });
    }
    return true;
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.remoteAgentSelectAgentId: {
        this.setToRemoteAgent(msg.data.agentId);
        return true;
      }
    }
    return false;
  }
}
