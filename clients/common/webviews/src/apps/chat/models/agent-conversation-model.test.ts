import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { ToolsWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/tool-messages";
import { RulesWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";
import { get } from "svelte/store";
import { beforeEach, describe, expect, test, vi } from "vitest";
import { AgentConversationModel } from "./agent-conversation-model";
import { ChatModel } from "./chat-model";
import { SpecialContextInputModel } from "./context-model";
import { ToolsWebviewModel } from "./tools-webview-model";
import { ExchangeWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/exchange-messages";
import { _DO_NOT_USE_IN_APP_CODE_unlockAllChains } from "../utils/locking-chain";
import { computeAgentExchangeStatus } from "./agent-conversation-utils";
import { AgentExchangeStatus, ExchangeStatus } from "../types/chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolUsePhase } from "../types/tool-use-state";

describe("AgentConversationModel", () => {
  let chatModel: ChatModel;
  let agentModel: AgentConversationModel;
  let contextModel: SpecialContextInputModel;
  let messageBroker: MessageBroker;

  beforeEach(() => {
    _DO_NOT_USE_IN_APP_CODE_unlockAllChains();
    host.postMessage.mockImplementation((msg) => {
      // Give up main thread to force some aynchronousity
      setTimeout(() => {
        switch (msg.type) {
          case WebViewMessageType.asyncWrapper: {
            const baseMsg = msg.baseMsg;
            if (!baseMsg) {
              throw new Error("No base message given with async wrapper");
            }
            switch (baseMsg.type) {
              case WebViewMessageType.chatLoaded: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.chatInitialize,
                        data: {
                          enableAgentMode: true,
                          memoryClassificationOnFirstToken: false,
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.chatModeChanged: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.openConfirmationModal: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.confirmationModalResponse,
                        data: {
                          ok: true,
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.chatGetAgentOnboardingPromptRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.chatGetAgentOnboardingPromptResponse,
                        data: {
                          prompt: "Onboarding prompt",
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.reportAgentSessionEvent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.checkAgentAutoModeApproval: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.checkAgentAutoModeApproval,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.getChatRequestIdeStateRequest: {
                // This is just mocked and not used in any of the tests below.
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getChatRequestIdeStateResponse,
                        data: {},
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.getWorkspaceInfoRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getWorkspaceInfoResponse,
                        data: {
                          trackedFileCount: [1000, 500], // Mock tracked file counts
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.setCurrentConversation:
              case ToolsWebViewMessageType.closeAllToolProcesses: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case "delete-conversation-tooluse-states-request": {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: "delete-conversation-tooluse-states-response",
                        data: {},
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.getEditListRequest: {
                break;
              }
              case AgentWebViewMessageType.checkHasEverUsedAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: AgentWebViewMessageType.checkHasEverUsedAgentResponse,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.setHasEverUsedAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.checkHasEverUsedRemoteAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.setHasEverUsedRemoteAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.getRemoteAgentNotificationEnabledRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse,
                        data: {}, // Empty notification settings
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.getRemoteAgentPinnedStatusRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getRemoteAgentPinnedStatusResponse,
                        data: {}, // Empty pinned status
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.getRemoteAgentOverviewsRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getRemoteAgentOverviewsResponse,
                        data: { overviews: [] }, // Empty overviews
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.getRemoteAgentStatus:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.remoteAgentStatusResponse,
                        data: { isRemoteAgentSshWindow: false },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;

              case WebViewMessageType.cancelRemoteAgentsStreamRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.remoteAgentOverviewsStreamRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.remoteAgentOverviewsStreamResponse,
                        data: {
                          overviews: [],
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.reportRemoteAgentEvent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case ExchangeWebViewMessageType.deleteConversationExchangesRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case "load-conversation-tooluse-states-request": {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: "load-conversation-tooluse-states-response",
                        data: { toolUseStates: {} },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case RulesWebViewMessageType.getRulesListRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: RulesWebViewMessageType.getRulesListResponse,
                        data: { rules: [] },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              default:
                throw new Error(`Unexpected async message type: ${baseMsg.type}`);
            }
            break;
          }
          case WebViewMessageType.chatClearMetadata:
            break;
          case WebViewMessageType.triggerInitialOrientation:
            break;
          default:
            throw new Error(`Unexpected message type: ${msg.type}`);
        }
      }, 0);
    });
    messageBroker = new MessageBroker(host);
    contextModel = new SpecialContextInputModel();
    chatModel = new ChatModel(messageBroker, host, contextModel, {
      initialFlags: {
        enableBackgroundAgents: true,
      },
    });

    // Create a mock checkpoint store
    const mockCheckpointStore = {
      registerAgentConversationModel: vi.fn(),
      targetCheckpointSummary: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      targetCheckpointHasChanges: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      currentCheckpoint: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      createNewCheckpoint: vi.fn(),
      maybeCreateOriginalCheckpoint: vi.fn(),
    };

    const mockSoundModel = {
      refreshSettings: vi.fn().mockResolvedValue(undefined),
      playAgentComplete: vi.fn().mockResolvedValue(undefined),
    } as any;

    // Create the agent model with the mock checkpoint store
    agentModel = new AgentConversationModel(
      chatModel,
      new ToolsWebviewModel(
        chatModel.currentConversationModel,
        chatModel.extensionClient,
        chatModel,
        mockSoundModel,
        undefined, // remoteAgentsModel not needed for these tests
      ),
      mockCheckpointStore as any,
      mockSoundModel,
    );

    // Initialize with a default conversation
    const defaultConv = {
      id: "default-conv",
      chatHistory: [],
      extraData: {},
      createdAtIso: new Date().toISOString(),
      lastInteractedAtIso: new Date().toISOString(),
      feedbackStates: {},
      toolUseStates: {},
      requestIds: [],
    };
    chatModel.currentConversationModel.setConversation(defaultConv);
  });

  describe("Agent hasEverUsedAgent", () => {
    test("first load should be undefined", () => {
      expect(get(agentModel.hasEverUsedAgent)).toBe(undefined);
    });

    test("user with agent chat thread should be true", () => {
      chatModel.currentConversationModel.setConversation({
        id: "agent-conv",
        chatHistory: [],
        extraData: { isAgentConversation: true },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      });

      // Create a mock checkpoint store
      const mockCheckpointStore = {
        registerAgentConversationModel: vi.fn(),
        targetCheckpointSummary: {
          subscribe: vi.fn(() => {
            return { unsubscribe: vi.fn() };
          }),
        },
        targetCheckpointHasChanges: {
          subscribe: vi.fn(() => {
            return { unsubscribe: vi.fn() };
          }),
        },
        currentCheckpoint: {
          subscribe: vi.fn(() => {
            return { unsubscribe: vi.fn() };
          }),
        },
        createNewCheckpoint: vi.fn(),
        maybeCreateOriginalCheckpoint: vi.fn(),
      };

      const mockSoundModel = {
        refreshSettings: vi.fn().mockResolvedValue(undefined),
        playAgentComplete: vi.fn().mockResolvedValue(undefined),
      } as any;

      // Create the agent model with the mock checkpoint store
      const testAgentModel = new AgentConversationModel(
        chatModel,
        new ToolsWebviewModel(
          chatModel.currentConversationModel,
          chatModel.extensionClient,
          chatModel,
          mockSoundModel,
          undefined, // remoteAgentsModel not needed for these tests
        ),
        mockCheckpointStore as any,
        mockSoundModel,
      );
      expect(get(testAgentModel.hasEverUsedAgent)).toBe(true);
    });

    test("user without agent chat thread and no context should be false", async () => {
      const spy = vi
        .spyOn(chatModel.extensionClient, "checkHasEverUsedAgent")
        .mockResolvedValue(false);

      expect(get(agentModel.hasEverUsedAgent)).toBe(undefined);

      await agentModel.refreshHasEverUsedAgent();

      expect(get(agentModel.hasEverUsedAgent)).toBe(false);

      expect(spy).toHaveBeenCalled();
    });

    test("user without agent chat thread and context should be true", async () => {
      const spy = vi
        .spyOn(chatModel.extensionClient, "checkHasEverUsedAgent")
        .mockResolvedValue(true);

      expect(get(agentModel.hasEverUsedAgent)).toBe(undefined);

      await agentModel.refreshHasEverUsedAgent();

      expect(get(agentModel.hasEverUsedAgent)).toBe(true);

      expect(spy).toHaveBeenCalled();
    });
  });

  describe("sound effects", () => {
    test("should not play agent complete sound during initialization", () => {
      const mockCheckpointStore = {
        registerAgentConversationModel: vi.fn(),
        targetCheckpointSummary: { subscribe: vi.fn(() => vi.fn()) },
        targetCheckpointHasChanges: { subscribe: vi.fn(() => vi.fn()) },
        createNewCheckpoint: vi.fn(),
        maybeCreateOriginalCheckpoint: vi.fn(),
      };

      const mockSoundModel = {
        refreshSettings: vi.fn().mockResolvedValue(undefined),
        playAgentComplete: vi.fn().mockResolvedValue(undefined),
      } as any;

      // Set up an existing agent conversation that would trigger the bug
      const existingAgentConversation = {
        id: "existing-agent-conv",
        chatHistory: [],
        extraData: { isAgentConversation: true }, // This is the key trigger
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      };

      // Set the conversation before creating the AgentConversationModel
      chatModel.currentConversationModel.setConversation(existingAgentConversation);

      // Create a spy to track sound calls
      const playAgentCompleteSpy = vi.spyOn(mockSoundModel, "playAgentComplete");

      // Act: Create the AgentConversationModel (this is where the bug would occur)
      const testAgentModel = new AgentConversationModel(
        chatModel,
        new ToolsWebviewModel(
          chatModel.currentConversationModel,
          chatModel.extensionClient,
          chatModel,
          mockSoundModel,
          undefined, // remoteAgentsModel not needed for these tests
        ),
        mockCheckpointStore as any,
        mockSoundModel,
      );

      // Assert: Sound should NOT have been played during construction
      expect(playAgentCompleteSpy).not.toHaveBeenCalled();

      // Cleanup
      testAgentModel.dispose();
    });
  });

  describe("Agent state transitions with parallel tools", () => {
    test("should not transition to notRunning when parallel tools are in early phases", () => {
      // Create a mock conversation model with parallel tools enabled
      const mockConversationModel = {
        chatHistory: [
          {
            request_id: "test-request-1",
            request_message: "Test user message",
            response_text: "Test response",
            status: ExchangeStatus.success,
            structured_output_nodes: [
              {
                id: 1,
                type: ChatResultNodeType.TOOL_USE,
                content: "",
                tool_use: {
                  tool_use_id: "tool-1",
                  tool_name: "github-api",
                  input_json: "{}",
                },
              },
              {
                id: 2,
                type: ChatResultNodeType.TOOL_USE,
                content: "",
                tool_use: {
                  tool_use_id: "tool-2",
                  tool_name: "linear",
                  input_json: "{}",
                },
              },
            ],
          },
        ],
        getToolUseState: (requestId: string, toolUseId: string) => {
          if (toolUseId === "tool-1") {
            return { phase: ToolUsePhase.new, requestId, toolUseId };
          }
          if (toolUseId === "tool-2") {
            return { phase: ToolUsePhase.running, requestId, toolUseId };
          }
          return { phase: ToolUsePhase.unknown, requestId, toolUseId };
        },
      } as any;

      const mockFlagsModel = {
        enableParallelTools: true,
      } as any;

      const status = computeAgentExchangeStatus(mockConversationModel, mockFlagsModel);

      // Should be running because tool-2 is running, even though tool-1 is in 'new' phase
      expect(status).toBe(AgentExchangeStatus.running);
    });

    test("should handle case where onlyDisplayableToolNodes filters out all nodes", () => {
      // Create a scenario where all tools are in phases that get filtered out
      const mockConversationModel = {
        chatHistory: [
          {
            request_id: "test-request-2",
            request_message: "Test user message",
            response_text: "Test response",
            status: ExchangeStatus.success,
            structured_output_nodes: [
              {
                id: 1,
                type: ChatResultNodeType.TOOL_USE,
                content: "",
                tool_use: {
                  tool_use_id: "tool-1",
                  tool_name: "github-api",
                  input_json: "{}",
                },
              },
              {
                id: 2,
                type: ChatResultNodeType.TOOL_USE,
                content: "",
                tool_use: {
                  tool_use_id: "tool-2",
                  tool_name: "linear",
                  input_json: "{}",
                },
              },
            ],
          },
        ],
        getToolUseState: (requestId: string, toolUseId: string) => {
          if (toolUseId === "tool-1") {
            return { phase: ToolUsePhase.new, requestId, toolUseId };
          }
          if (toolUseId === "tool-2") {
            return { phase: ToolUsePhase.checkingSafety, requestId, toolUseId };
          }
          return { phase: ToolUsePhase.unknown, requestId, toolUseId };
        },
      } as any;

      const mockFlagsModel = {
        enableParallelTools: true,
      } as any;

      const status = computeAgentExchangeStatus(mockConversationModel, mockFlagsModel);

      // Should still be running because our fix ensures we fall back to the last tool node
      // when onlyDisplayableToolNodes filters out everything
      expect(status).toBe(AgentExchangeStatus.running);
    });

    test("should correctly identify last tool when parallel tools are enabled", () => {
      const mockConversationModel = {
        chatHistory: [
          {
            request_id: "test-request-3",
            request_message: "Test user message",
            response_text: "Test response",
            status: ExchangeStatus.success,
            structured_output_nodes: [
              {
                id: 1,
                type: ChatResultNodeType.TOOL_USE,
                content: "",
                tool_use: {
                  tool_use_id: "tool-1",
                  tool_name: "github-api",
                  input_json: "{}",
                },
              },
              {
                id: 2,
                type: ChatResultNodeType.TOOL_USE,
                content: "",
                tool_use: {
                  tool_use_id: "tool-2",
                  tool_name: "linear",
                  input_json: "{}",
                },
              },
            ],
          },
        ],
        getToolUseState: (requestId: string, toolUseId: string) => {
          if (toolUseId === "tool-1") {
            return { phase: ToolUsePhase.completed, requestId, toolUseId };
          }
          if (toolUseId === "tool-2") {
            return { phase: ToolUsePhase.cancelled, requestId, toolUseId };
          }
          return { phase: ToolUsePhase.unknown, requestId, toolUseId };
        },
      } as any;

      const mockFlagsModel = {
        enableParallelTools: true,
      } as any;

      const status = computeAgentExchangeStatus(mockConversationModel, mockFlagsModel);

      // Should be notRunning because the last tool (tool-2) is cancelled
      expect(status).toBe(AgentExchangeStatus.notRunning);
    });

    test("should handle edge case with no tool nodes", () => {
      // Create a scenario where there are no tool use nodes at all
      const mockConversationModel = {
        chatHistory: [
          {
            request_id: "test-request-4",
            request_message: "Test user message",
            response_text: "Test response",
            status: ExchangeStatus.success,
            structured_output_nodes: [], // No tool nodes
          },
        ],
        getToolUseState: (requestId: string, toolUseId: string) => {
          return { phase: ToolUsePhase.unknown, requestId, toolUseId };
        },
      } as any;

      const mockFlagsModel = {
        enableParallelTools: true,
      } as any;

      const status = computeAgentExchangeStatus(mockConversationModel, mockFlagsModel);

      // Should be notRunning because there are no tool nodes
      expect(status).toBe(AgentExchangeStatus.notRunning);
    });
  });
});
