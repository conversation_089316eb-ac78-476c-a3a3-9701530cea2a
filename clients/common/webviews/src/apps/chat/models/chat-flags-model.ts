import { type EloModelConfiguration } from "$vscode/src/webview-panels/preference-panel-types";
import {
  SmartPastePrecomputeMode,
  type UserTier,
} from "$vscode/src/webview-providers/webview-messages";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { type ModelInfoRegistryEntry } from "@augment-internal/sidecar-libs/src/api/types";
import { EXPERIMENT_TREATMENTS, type ExperimentTreatment } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import { type Readable } from "svelte/store";
import { type IChatFlags } from "./types";

export const DEFAULT_SMALL_SYNC_THRESHOLD = 15;
export const DEFAULT_BIG_SYNC_THRESHOLD = 1000;
export const DEFAULT_MAX_TRACKABLE_FILE_COUNT = 250_000;
export const DEFAULT_IDLE_MESSAGE_TIMEOUT_MS = 1000 * 60 * 5; // 5 minutes
export const DEFAULT_IDLE_NOTIFICATION_TIMEOUT_MS = 1000 * 60 * 60; // 1 hour
export const DEFAULT_MAX_CHAT_INPUT_CHARS = 20_000;

class ChatFlagsModel implements Readable<IChatFlags> {
  private _enableEditableHistory = false;
  private _enablePreferenceCollection = false;
  private _enableRetrievalDataCollection = false;
  private _enableDebugFeatures = false;
  private _enableConversationDebugUtils = false;
  private _enableRichTextHistory = false;
  private _enableAgentSwarmMode = false;
  private _modelDisplayNameToId: { [model: string]: string | null } = {};
  private _fullFeatured = true;
  private _enableExternalSourcesInChat = false;
  private _smallSyncThreshold = DEFAULT_SMALL_SYNC_THRESHOLD;
  private _bigSyncThreshold = DEFAULT_BIG_SYNC_THRESHOLD;
  private _enableSmartPaste = false;
  private _enableDirectApply = false;
  private _summaryTitles = false;
  private _suggestedEditsAvailable = false;
  private _enableShareService = false;
  private _maxTrackableFileCount = DEFAULT_MAX_TRACKABLE_FILE_COUNT;
  private _enableDesignSystemRichTextEditor = false;
  private _enableSources = false;
  private _enableChatMermaidDiagrams = false;
  private _smartPastePrecomputeMode = SmartPastePrecomputeMode.visibleHover;
  private _useNewThreadsMenu = false;
  private _enableChatMermaidDiagramsMinVersion = false;
  private _enablePromptEnhancer = false;
  private _idleNewSessionNotificationTimeoutMs: undefined | number;
  private _idleNewSessionMessageTimeoutMs: undefined | number;
  private _enableChatMultimodal = false;
  private _enableAgentMode = false;
  private _enableAgentAutoMode = false;
  private _enableRichCheckpointInfo = false;
  private _agentMemoriesFilePathName: IQualifiedPathName | undefined;
  private _conversationHistorySizeThresholdBytes = 42 * 1024 * 1024; // Default 42MB
  private _userTier: UserTier = "unknown";
  private _eloModelConfiguration: EloModelConfiguration = {
    highPriorityModels: [],
    regularBattleModels: [],
    highPriorityThreshold: 0.5,
  };
  private _truncateChatHistory = false;
  private _enableBackgroundAgents = false;
  private _enableNewThreadsList = false;
  private _customPersonalityPrompts: {
    agent?: string;
    prototyper?: string;
    brainstorm?: string;
    reviewer?: string;
  } = {};
  private _enablePersonalities = false;
  private _enableRules = false;
  private _memoryClassificationOnFirstToken = false;
  private _enableGenerateCommitMessage = false;
  private _modelRegistry: Record<string, string> = {};
  private _modelInfoRegistry: Record<string, ModelInfoRegistryEntry> = {};
  private _agentChatModel: string = "";
  private _enableModelRegistry = false;
  private _enableTaskList = false;
  private _clientAnnouncement = "";
  private _useHistorySummary = false;
  private _historySummaryParams = "";
  private _enableExchangeStorage = false;
  private _enableToolUseStateStorage = false;
  private _retryChatStreamTimeouts = false;
  private _enableCommitIndexing = false;
  private _enableMemoryRetrieval = false;
  private _enableAgentTabs = false;
  private _isVscodeVersionOutdated = false;
  private _vscodeMinVersion = "";
  private _enableGroupedTools = false;
  private _remoteAgentsResumeHintAvailableTtlDays = 0;
  private _enableParallelTools = false;
  private _enableAgentGitTracker = false;
  private _memoriesParams: { [key: string]: string | number | boolean } = {};
  private _nonDismissibleBannerTestTreatment: ExperimentTreatment = EXPERIMENT_TREATMENTS.OFF;

  private _subscribers: Set<(chatFlags: ChatFlagsModel) => void> = new Set();

  constructor(initialFlags?: Partial<IChatFlags>) {
    if (initialFlags) {
      this.update(initialFlags);
    }
  }

  subscribe = (sub: (chatFlags: ChatFlagsModel) => void): (() => void) => {
    this._subscribers.add(sub);
    sub(this);
    return () => {
      this._subscribers.delete(sub);
    };
  };

  update = (flags: Partial<IChatFlags>) => {
    this._enableEditableHistory = flags.enableEditableHistory ?? this._enableEditableHistory;
    this._enablePreferenceCollection =
      flags.enablePreferenceCollection ?? this._enablePreferenceCollection;
    this._enableRetrievalDataCollection =
      flags.enableRetrievalDataCollection ?? this._enableRetrievalDataCollection;
    this._enableDebugFeatures = flags.enableDebugFeatures ?? this._enableDebugFeatures;
    this._enableConversationDebugUtils =
      flags.enableConversationDebugUtils ?? this._enableConversationDebugUtils;
    this._enableRichTextHistory = flags.enableRichTextHistory ?? this._enableRichTextHistory;
    this._enableAgentSwarmMode = flags.enableAgentSwarmMode ?? this._enableAgentSwarmMode;
    this._modelDisplayNameToId = { ...flags.modelDisplayNameToId };
    this._fullFeatured = flags.fullFeatured ?? this._fullFeatured;
    this._enableExternalSourcesInChat =
      flags.enableExternalSourcesInChat ?? this._enableExternalSourcesInChat;
    this._smallSyncThreshold = flags.smallSyncThreshold ?? this._smallSyncThreshold;
    this._bigSyncThreshold = flags.bigSyncThreshold ?? this._bigSyncThreshold;
    this._enableSmartPaste = flags.enableSmartPaste ?? this._enableSmartPaste;
    this._enableDirectApply = flags.enableDirectApply ?? this._enableDirectApply;
    this._summaryTitles = flags.summaryTitles ?? this._summaryTitles;
    this._suggestedEditsAvailable = flags.suggestedEditsAvailable ?? this._suggestedEditsAvailable;
    this._enableShareService = flags.enableShareService ?? this._enableShareService;
    this._maxTrackableFileCount = flags.maxTrackableFileCount ?? this._maxTrackableFileCount;
    this._enableDesignSystemRichTextEditor =
      flags.enableDesignSystemRichTextEditor ?? this._enableDesignSystemRichTextEditor;
    this._enableSources = flags.enableSources ?? this._enableSources;
    this._enableChatMermaidDiagrams =
      flags.enableChatMermaidDiagrams ?? this._enableChatMermaidDiagrams;
    this._smartPastePrecomputeMode =
      flags.smartPastePrecomputeMode ?? this._smartPastePrecomputeMode;
    this._useNewThreadsMenu = flags.useNewThreadsMenu ?? this._useNewThreadsMenu;
    this._enableChatMermaidDiagramsMinVersion =
      flags.enableChatMermaidDiagramsMinVersion ?? this._enableChatMermaidDiagramsMinVersion;
    this._enablePromptEnhancer = flags.enablePromptEnhancer ?? this._enablePromptEnhancer;

    this._idleNewSessionMessageTimeoutMs =
      flags.idleNewSessionMessageTimeoutMs ??
      (flags.enableDebugFeatures
        ? (this._idleNewSessionMessageTimeoutMs ?? DEFAULT_IDLE_MESSAGE_TIMEOUT_MS)
        : this._idleNewSessionMessageTimeoutMs);

    this._idleNewSessionNotificationTimeoutMs = flags.idleNewSessionNotificationTimeoutMs ?? 0;
    this._enableChatMultimodal = flags.enableChatMultimodal ?? this._enableChatMultimodal;
    this._enableAgentMode = flags.enableAgentMode ?? this._enableAgentMode;
    this._enableAgentAutoMode = flags.enableAgentAutoMode ?? this._enableAgentAutoMode;
    this._enableRichCheckpointInfo =
      flags.enableRichCheckpointInfo ?? this._enableRichCheckpointInfo;
    this._agentMemoriesFilePathName =
      flags.agentMemoriesFilePathName ?? this._agentMemoriesFilePathName;
    this._conversationHistorySizeThresholdBytes =
      flags.conversationHistorySizeThresholdBytes ?? this._conversationHistorySizeThresholdBytes;
    this._userTier = flags.userTier ?? this._userTier;
    this._eloModelConfiguration = flags.eloModelConfiguration ?? this._eloModelConfiguration;
    this._truncateChatHistory = flags.truncateChatHistory ?? this._truncateChatHistory;
    this._enableBackgroundAgents = flags.enableBackgroundAgents ?? this._enableBackgroundAgents;
    this._enableNewThreadsList = flags.enableNewThreadsList ?? this._enableNewThreadsList;

    this._customPersonalityPrompts =
      flags.customPersonalityPrompts ?? this._customPersonalityPrompts;
    this._enablePersonalities = flags.enablePersonalities ?? this._enablePersonalities;
    this._enableRules = flags.enableRules ?? this._enableRules;
    this._memoryClassificationOnFirstToken =
      flags.memoryClassificationOnFirstToken ?? this._memoryClassificationOnFirstToken;

    this._enableGenerateCommitMessage =
      flags.enableGenerateCommitMessage ?? this._enableGenerateCommitMessage;
    this._modelRegistry = flags.modelRegistry ?? this._modelRegistry;
    this._modelInfoRegistry = flags.modelInfoRegistry ?? this._modelInfoRegistry;
    this._enableModelRegistry = flags.enableModelRegistry ?? this._enableModelRegistry;
    this._agentChatModel = flags.agentChatModel ?? this._agentChatModel;
    this._enableTaskList = flags.enableTaskList ?? this._enableTaskList;
    this._clientAnnouncement = flags.clientAnnouncement ?? this._clientAnnouncement;
    this._useHistorySummary = flags.useHistorySummary ?? this._useHistorySummary;
    this._historySummaryParams = flags.historySummaryParams ?? this._historySummaryParams;
    this._enableExchangeStorage = flags.enableExchangeStorage ?? this._enableExchangeStorage;
    this._retryChatStreamTimeouts = flags.retryChatStreamTimeouts ?? this._retryChatStreamTimeouts;
    this._enableCommitIndexing = flags.enableCommitIndexing ?? this._enableCommitIndexing;
    this._enableMemoryRetrieval = flags.enableMemoryRetrieval ?? this._enableMemoryRetrieval;
    this._enableAgentTabs = flags.enableAgentTabs ?? this._enableAgentTabs;
    this._isVscodeVersionOutdated = flags.isVscodeVersionOutdated ?? this._isVscodeVersionOutdated;
    this._vscodeMinVersion = flags.vscodeMinVersion ?? this._vscodeMinVersion;
    this._enableGroupedTools = flags.enableGroupedTools ?? this._enableGroupedTools;
    this._remoteAgentsResumeHintAvailableTtlDays =
      flags.remoteAgentsResumeHintAvailableTtlDays ?? this._remoteAgentsResumeHintAvailableTtlDays;
    this._enableToolUseStateStorage =
      flags.enableToolUseStateStorage ?? this._enableToolUseStateStorage;
    this._enableParallelTools = flags.enableParallelTools ?? this._enableParallelTools;
    this._enableAgentGitTracker = flags.enableAgentGitTracker ?? this._enableAgentGitTracker;
    this._memoriesParams = flags.memoriesParams ?? this._memoriesParams;
    const _treatment = flags.nonDismissibleBannerTestTreatment;
    if (
      _treatment === EXPERIMENT_TREATMENTS.OFF ||
      _treatment === EXPERIMENT_TREATMENTS.CONTROL ||
      _treatment === EXPERIMENT_TREATMENTS.V0
    ) {
      this._nonDismissibleBannerTestTreatment = _treatment;
    }

    this._subscribers.forEach((sub) => sub(this));
  };

  get enableEditableHistory(): boolean {
    return this._fullFeatured && (this._enableEditableHistory || this._enableDebugFeatures);
  }

  get enablePreferenceCollection(): boolean {
    return this._enablePreferenceCollection;
  }

  get enableRetrievalDataCollection(): boolean {
    return this._enableRetrievalDataCollection;
  }

  get enableDebugFeatures(): boolean {
    return this._enableDebugFeatures;
  }

  get enableConversationDebugUtils(): boolean {
    return this._enableConversationDebugUtils || this._enableDebugFeatures;
  }

  get enableGenerateCommitMessage(): boolean {
    return this._enableGenerateCommitMessage;
  }

  get enableRichTextHistory(): boolean {
    return this._enableRichTextHistory || this._enableDebugFeatures;
  }

  get enableAgentSwarmMode(): boolean {
    return this._enableAgentSwarmMode;
  }

  get modelDisplayNameToId(): { [model: string]: string | null } {
    return this._modelDisplayNameToId;
  }

  get orderedModelDisplayNames(): string[] {
    return Object.keys(this._modelDisplayNameToId).sort((a, b) => {
      const aLower = a.toLowerCase();
      const bLower = b.toLowerCase();

      // Check if either string is "default" (case-insensitive)
      if (aLower === "default" && bLower !== "default") {
        return -1; // a should come before b
      }
      if (bLower === "default" && aLower !== "default") {
        return 1; // b should come before a
      }

      // If neither or both are "default", sort alphabetically
      return a.localeCompare(b);
    });
  }

  get fullFeatured(): boolean {
    return this._fullFeatured;
  }

  get enableExternalSourcesInChat(): boolean {
    return this._enableExternalSourcesInChat;
  }

  get smallSyncThreshold(): number {
    return this._smallSyncThreshold;
  }

  get bigSyncThreshold(): number {
    return this._bigSyncThreshold;
  }

  get enableSmartPaste(): boolean {
    return this._enableDebugFeatures || this._enableSmartPaste;
  }

  get enableDirectApply(): boolean {
    return this._enableDirectApply || this._enableDebugFeatures;
  }

  get enableShareService(): boolean {
    return this._enableShareService;
  }

  get summaryTitles(): boolean {
    return this._summaryTitles;
  }

  get suggestedEditsAvailable(): boolean {
    return this._suggestedEditsAvailable;
  }

  get maxTrackableFileCount(): number {
    return this._maxTrackableFileCount;
  }

  get enableSources(): boolean {
    return this._enableDebugFeatures || this._enableSources;
  }

  get enableChatMermaidDiagrams(): boolean {
    return this._enableDebugFeatures || this._enableChatMermaidDiagrams;
  }

  get smartPastePrecomputeMode(): SmartPastePrecomputeMode {
    return this._smartPastePrecomputeMode;
  }

  get useNewThreadsMenu(): boolean {
    return this._useNewThreadsMenu;
  }

  get enableChatMermaidDiagramsMinVersion(): boolean {
    return this._enableChatMermaidDiagramsMinVersion;
  }

  get enablePromptEnhancer(): boolean {
    return this._enablePromptEnhancer;
  }

  get enableDesignSystemRichTextEditor(): boolean {
    return this._enableDesignSystemRichTextEditor;
  }

  get idleNewSessionNotificationTimeoutMs(): number {
    return this._idleNewSessionNotificationTimeoutMs ?? 0;
  }

  get idleNewSessionMessageTimeoutMs(): number {
    return this._idleNewSessionMessageTimeoutMs ?? 0;
  }

  get enableChatMultimodal(): boolean {
    return this._enableChatMultimodal;
  }

  get enableAgentMode(): boolean {
    return this._enableAgentMode;
  }

  get enableAgentAutoMode(): boolean {
    return this._enableAgentAutoMode;
  }

  get enableRichCheckpointInfo(): boolean {
    return this._enableRichCheckpointInfo;
  }

  get agentMemoriesFilePathName(): IQualifiedPathName | undefined {
    return this._agentMemoriesFilePathName;
  }

  get conversationHistorySizeThresholdBytes(): number {
    return this._conversationHistorySizeThresholdBytes;
  }

  get userTier(): UserTier {
    return this._userTier;
  }

  get eloModelConfiguration(): EloModelConfiguration {
    return this._eloModelConfiguration;
  }

  get truncateChatHistory(): boolean {
    return this._truncateChatHistory;
  }

  get enableBackgroundAgents(): boolean {
    return this._enableBackgroundAgents;
  }

  get enableNewThreadsList(): boolean {
    return this._enableNewThreadsList;
  }

  get customPersonalityPrompts(): {
    agent?: string;
    prototyper?: string;
    brainstorm?: string;
    reviewer?: string;
  } {
    return this._customPersonalityPrompts;
  }

  get enablePersonalities(): boolean {
    return this._enablePersonalities || this._enableDebugFeatures;
  }

  get enableRules(): boolean {
    return this._enableRules;
  }

  get memoryClassificationOnFirstToken(): boolean {
    return this._memoryClassificationOnFirstToken;
  }

  get agentChatModel(): string {
    return this._agentChatModel;
  }

  get modelRegistry(): Record<string, string> {
    return this._modelRegistry;
  }

  get modelInfoRegistry(): Record<string, ModelInfoRegistryEntry> {
    return this._modelInfoRegistry;
  }

  get enableModelRegistry(): boolean {
    return this._enableModelRegistry;
  }

  get enableTaskList(): boolean {
    return this._enableTaskList;
  }

  get clientAnnouncement(): string {
    return this._clientAnnouncement;
  }

  get useHistorySummary(): boolean {
    return this._useHistorySummary;
  }

  get historySummaryParams(): string {
    return this._historySummaryParams;
  }

  get enableExchangeStorage(): boolean {
    return this._enableExchangeStorage;
  }

  get enableToolUseStateStorage(): boolean {
    return this._enableToolUseStateStorage;
  }

  get retryChatStreamTimeouts(): boolean {
    return this._retryChatStreamTimeouts;
  }

  get enableCommitIndexing(): boolean {
    return this._enableCommitIndexing;
  }

  get enableMemoryRetrieval(): boolean {
    return this._enableMemoryRetrieval;
  }

  get enableAgentTabs(): boolean {
    return this._enableAgentTabs;
  }

  get isVscodeVersionOutdated(): boolean {
    return this._isVscodeVersionOutdated;
  }

  get vscodeMinVersion(): string {
    return this._vscodeMinVersion;
  }

  get enableErgonomicsUpdate(): boolean {
    return this._enableDebugFeatures;
  }

  get enableGroupedTools(): boolean {
    return this._enableGroupedTools;
  }

  get remoteAgentsResumeHintAvailableTtlDays(): number {
    return this._remoteAgentsResumeHintAvailableTtlDays;
  }

  // Check that the model ID is in the model display name to ID map, or is in the model registry
  isModelIdValid = (modelId: string | null | undefined): boolean => {
    if (modelId === undefined) {
      return false;
    }
    return (
      Object.values(this._modelDisplayNameToId).includes(modelId) ||
      Object.values(this._modelRegistry).includes(modelId ?? "") ||
      Object.keys(this._modelInfoRegistry).includes(modelId ?? "")
    );
  };

  get enableParallelTools(): boolean {
    return this._enableParallelTools;
  }

  get enableAgentGitTracker(): boolean {
    return this._enableAgentGitTracker;
  }

  get memoriesParams(): { [key: string]: string | number | boolean } {
    return this._memoriesParams;
  }

  getModelDisplayName = (modelId: string | null | undefined): string | undefined => {
    if (modelId === undefined) {
      return undefined;
    }
    return Object.keys(this._modelDisplayNameToId).find(
      (model) => this._modelDisplayNameToId[model] === modelId,
    );
  };

  get nonDismissibleBannerTestTreatment(): ExperimentTreatment {
    return this._nonDismissibleBannerTestTreatment;
  }
}

export { ChatFlagsModel };
