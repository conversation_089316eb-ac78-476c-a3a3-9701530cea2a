import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import {
  ModeSelectorAction,
  ModeSelectorMode,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import { tick } from "svelte";
import { get } from "svelte/store";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { waitFor } from "@testing-library/dom";
import { ChatModeModel } from "./chat-mode-model";
import { AgentConversationModel } from "./agent-conversation-model";
import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { AgentExecutionMode, ChatModel } from "./chat-model";
import { SpecialContextInputModel } from "./context-model";
import { ToolsWebviewModel } from "./tools-webview-model";
import { _DO_NOT_USE_IN_APP_CODE_unlockAllChains } from "../utils/locking-chain";

describe("ChatModeModel", () => {
  let chatModel: ChatModel;
  let agentConversationModel: AgentConversationModel;
  let remoteAgentsModel: RemoteAgentsModel;
  let chatModeModel: ChatModeModel;
  let messageBroker: MessageBroker;
  let contextModel: SpecialContextInputModel;
  let mockCheckpointStore: any;
  let mockToolsModel: any;
  let mockSoundModel: any;

  beforeEach(() => {
    _DO_NOT_USE_IN_APP_CODE_unlockAllChains();
    // Setup host mock with proper async message handling
    host.postMessage.mockImplementation((msg) => {
      // Handle async messages immediately
      if (msg.type === WebViewMessageType.asyncWrapper) {
        const baseMsg = msg.baseMsg;
        if (!baseMsg) return;

        // Mock responses for different message types - use immediate response
        const response: any = {
          type: WebViewMessageType.asyncWrapper,
          requestId: msg.requestId,
          error: null,
          baseMsg: null,
          destination: "webview",
        };

        switch (baseMsg.type) {
          case "get-remote-agent-notification-enabled-request":
            response.baseMsg = {
              type: "get-remote-agent-notification-enabled-response",
              data: { enabledAgentIds: [] },
            };
            break;
          case "get-remote-agent-pinned-status-request":
            response.baseMsg = {
              type: "get-remote-agent-pinned-status-response",
              data: { pinnedAgents: {} },
            };
            break;
          case "chat-mode-changed":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "agent-set-current-conversation":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "get-workspace-info-request":
            response.baseMsg = {
              type: "get-workspace-info-response",
              data: { workspacePath: "/test/workspace" },
            };
            break;
          case "check-agent-auto-mode-approval":
            response.baseMsg = {
              type: "check-agent-auto-mode-approval-response",
              data: { approved: false },
            };
            break;
          case "check-has-ever-used-agent":
            response.baseMsg = {
              type: "check-has-ever-used-agent-response",
              data: { hasUsed: false },
            };
            break;
          case "check-has-ever-used-remote-agent":
            response.baseMsg = {
              type: "check-has-ever-used-remote-agent-response",
              data: { hasUsed: false },
            };
            break;
          case "get-remote-agent-overviews-request":
            response.baseMsg = {
              type: "get-remote-agent-overviews-response",
              data: { agents: [] },
            };
            break;
          case "report-agent-session-event":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "delete-conversation-tooluse-states-request":
            response.baseMsg = {
              type: "delete-conversation-tooluse-states-response",
              data: {},
            };
            break;
          case "load-conversation-tooluse-states-request":
            response.baseMsg = {
              type: "load-conversation-tooluse-states-response",
              data: { toolUseStates: {} },
            };
            break;
          case "report-remote-agent-event":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "get-remote-agent-status":
            response.baseMsg = {
              type: "remote-agent-status-response",
              data: {
                isRemoteAgentSshWindow: false,
                remoteAgentId: undefined,
              },
            };
            break;
          case "cancel-remote-agent-history-stream-request":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "cancel-remote-agents-stream-request":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "chat-set-last-used-chat-mode":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          default:
            // For unhandled messages, just return
            return;
        }

        // Simulate the response being sent back immediately
        // Use queueMicrotask to ensure it's async but immediate
        queueMicrotask(() => {
          window.postMessage(response, "*");
        });
      }
    });

    // Initialize dependencies
    messageBroker = new MessageBroker(host);
    contextModel = new SpecialContextInputModel();

    chatModel = new ChatModel(messageBroker, host, contextModel, {
      initialFlags: {
        enableBackgroundAgents: true,
        enableAgentAutoMode: true,
      },
    });

    const mockGitRefModel = {
      listUserRepos: vi.fn().mockResolvedValue({
        repos: [],
        error: undefined,
        isDevDeploy: false,
      }),
      isGithubAuthenticated: vi.fn().mockResolvedValue(true),
    } as any;

    remoteAgentsModel = new RemoteAgentsModel({
      msgBroker: messageBroker,
      isActive: false,
      flagsModel: chatModel.flags,
      host,
      gitRefModel: mockGitRefModel,
    });

    // Create mock checkpoint store
    mockCheckpointStore = {
      registerAgentConversationModel: vi.fn(),
      targetCheckpointSummary: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      targetCheckpointHasChanges: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      currentCheckpoint: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      createNewCheckpoint: vi.fn(),
      maybeCreateOriginalCheckpoint: vi.fn(),
    };

    // Create mock sound model
    mockSoundModel = {
      refreshSettings: vi.fn().mockResolvedValue(undefined),
      playAgentComplete: vi.fn().mockResolvedValue(undefined),
    };

    // Create mock tools model
    mockToolsModel = new ToolsWebviewModel(
      chatModel.currentConversationModel,
      chatModel.extensionClient,
      chatModel,
      mockSoundModel,
      undefined, // remoteAgentsModel not needed for these tests
    );

    agentConversationModel = new AgentConversationModel(
      chatModel,
      mockToolsModel,
      mockCheckpointStore,
      mockSoundModel,
    );

    chatModeModel = new ChatModeModel(chatModel, agentConversationModel, remoteAgentsModel);
  });

  afterEach(() => {
    // Clean up models to prevent timeout errors during test teardown
    if (remoteAgentsModel) {
      remoteAgentsModel.dispose();
    }
    if (chatModel) {
      // Clear any pending timeouts in the chat model
      vi.clearAllTimers();
    }
  });

  describe("Constructor", () => {
    test("should initialize with remote agent mode when isRemoteAgentSshWindow flag is true", async () => {
      // Set the remote agent window flag
      // there is no setter for this, so we have to mock the getter
      vi.spyOn(remoteAgentsModel, "isRemoteAgentSshWindow", "get").mockReturnValue(true);
      vi.spyOn(remoteAgentsModel, "remoteAgentSshWindowId", "get").mockReturnValue(
        "test-agent-123",
      );

      const remoteAgentChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
        },
      });

      const remoteAgentToolsModel = new ToolsWebviewModel(
        remoteAgentChatModel.currentConversationModel,
        remoteAgentChatModel.extensionClient,
        remoteAgentChatModel,
        mockSoundModel,
        undefined, // remoteAgentsModel not needed for these tests
      );

      const remoteAgentConversationModel = new AgentConversationModel(
        remoteAgentChatModel,
        remoteAgentToolsModel,
        mockCheckpointStore,
        mockSoundModel,
      );

      new ChatModeModel(remoteAgentChatModel, remoteAgentConversationModel, remoteAgentsModel);

      await tick();

      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });

  describe("cycleChatAgentModes", () => {
    describe("forward toggle", () => {
      test("should toggle from chat to agent manual mode", async () => {
        // Start in chat mode
        await chatModeModel.handleSetToThreadType("chat");
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);

        // Toggle forward
        await chatModeModel.cycleChatAgentModes(false);

        // Wait for the mode to be updated
        await waitFor(() => {
          expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
          expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
        });
      });

      test("should toggle from agent manual to agent auto mode", async () => {
        // Start in agent manual mode
        await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.manual);
        await tick();

        // Toggle forward
        await chatModeModel.cycleChatAgentModes(false);

        // Wait for the agent execution mode to be updated
        await waitFor(() => {
          expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
        });

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      });

      test("should toggle from agent auto to remote agent when enabled", async () => {
        // The enableBackgroundAgents flag is already true from the initial setup

        // Start in agent auto mode
        await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.auto);
        await tick();

        // Toggle forward
        await chatModeModel.cycleChatAgentModes(false);
        await tick();

        expect(remoteAgentsModel.isActive).toBe(true);
      });

      test("should toggle from agent auto to chat when remote agents disabled", async () => {
        // Create a new chat model with remote agents disabled
        const disabledChatModel = new ChatModel(messageBroker, host, contextModel, {
          initialFlags: {
            enableBackgroundAgents: false,
            enableAgentAutoMode: true,
          },
        });
        const setCurrentConversationSpy = vi.spyOn(disabledChatModel, "setCurrentConversation");
        setCurrentConversationSpy.mockResolvedValue(undefined);

        const disabledToolsModel = new ToolsWebviewModel(
          disabledChatModel.currentConversationModel,
          disabledChatModel.extensionClient,
          disabledChatModel,
          mockSoundModel,
          undefined, // remoteAgentsModel not needed for these tests
        );

        const disabledAgentConversationModel = new AgentConversationModel(
          disabledChatModel,
          disabledToolsModel,
          mockCheckpointStore,
          mockSoundModel,
        );

        const disabledChatModeModel = new ChatModeModel(
          disabledChatModel,
          disabledAgentConversationModel,
          remoteAgentsModel,
        );

        // Start in agent auto mode
        await disabledChatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.auto);
        await tick();

        // Toggle forward
        await disabledChatModeModel.cycleChatAgentModes(false);
        await tick();

        expect(get(disabledAgentConversationModel.isCurrConversationAgentic)).toBe(false);
        expect(remoteAgentsModel.isActive).toBe(false);
      });

      test("should toggle from remote agent back to chat", async () => {
        // Start in remote agent mode
        chatModeModel.setToRemoteAgent();
        await tick();

        expect(remoteAgentsModel.isActive).toBe(true);

        // Toggle forward
        await chatModeModel.cycleChatAgentModes(false);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
        expect(remoteAgentsModel.isActive).toBe(false);
      });
    });

    describe("reverse toggle", () => {
      test("should toggle from chat to remote agent when enabled", async () => {
        // The enableBackgroundAgents flag is already true from the initial setup

        // Start in chat mode
        await chatModeModel.handleSetToThreadType("chat");
        await tick();

        // Toggle reverse
        await chatModeModel.cycleChatAgentModes(true);
        await tick();

        expect(remoteAgentsModel.isActive).toBe(true);
      });

      test("should toggle from chat to agent auto when remote agents disabled", async () => {
        // Create a new chat model with remote agents disabled
        const disabledChatModel = new ChatModel(messageBroker, host, contextModel, {
          initialFlags: {
            enableBackgroundAgents: false,
            enableAgentAutoMode: true,
          },
        });

        const disabledToolsModel = new ToolsWebviewModel(
          disabledChatModel.currentConversationModel,
          disabledChatModel.extensionClient,
          disabledChatModel,
          mockSoundModel,
          undefined, // remoteAgentsModel not needed for these tests
        );

        const disabledAgentConversationModel = new AgentConversationModel(
          disabledChatModel,
          disabledToolsModel,
          mockCheckpointStore,
          mockSoundModel,
        );

        const disabledChatModeModel = new ChatModeModel(
          disabledChatModel,
          disabledAgentConversationModel,
          remoteAgentsModel,
        );

        // Start in chat mode
        await disabledChatModeModel.handleSetToThreadType("chat");
        await tick();

        // Toggle reverse
        await disabledChatModeModel.cycleChatAgentModes(true);
        await tick();

        expect(get(disabledAgentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(disabledChatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
      });

      test("should toggle from agent manual to chat", async () => {
        const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");
        setCurrentConversationSpy.mockResolvedValue(undefined);

        // Start in agent manual mode
        await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.manual);
        await tick();

        // Toggle reverse
        await chatModeModel.cycleChatAgentModes(true);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      });

      test("should toggle from agent auto to agent manual", async () => {
        // Start in agent auto mode
        await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.auto);
        await tick();

        // Toggle reverse
        await chatModeModel.cycleChatAgentModes(true);

        // Wait for the mode to be updated
        await waitFor(() => {
          expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
        });

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      });

      test("should toggle from remote agent to agent auto", async () => {
        chatModeModel.setToRemoteAgent();
        await tick();

        // Verify we're in remote agent mode
        expect(remoteAgentsModel.isActive).toBe(true);
        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);

        // Toggle reverse - from remote agent, it should go to agent auto mode
        await chatModeModel.cycleChatAgentModes(true);
        await tick();

        // The setToAgent method is called with auto mode
        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
        expect(remoteAgentsModel.isActive).toBe(false);
      });
    });
  });

  describe("setToThreadType", () => {
    test("should unset remote agents model", async () => {
      await chatModeModel.handleSetToThreadType("chat");

      expect(remoteAgentsModel.isActive).toBe(false);
    });

    test("should not change mode in remote agent window", async () => {
      // Set the remote agent window flag
      vi.spyOn(remoteAgentsModel, "isRemoteAgentSshWindow", "get").mockReturnValue(true);
      vi.spyOn(remoteAgentsModel, "remoteAgentSshWindowId", "get").mockReturnValue(
        "test-agent-123",
      );

      const remoteAgentChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
        },
      });

      const remoteAgentToolsModel = new ToolsWebviewModel(
        remoteAgentChatModel.currentConversationModel,
        remoteAgentChatModel.extensionClient,
        remoteAgentChatModel,
        mockSoundModel,
        undefined, // remoteAgentsModel not needed for these tests
      );

      const remoteAgentConversationModel = new AgentConversationModel(
        remoteAgentChatModel,
        remoteAgentToolsModel,
        mockCheckpointStore,
        mockSoundModel,
      );

      const remoteAgentChatModeModel = new ChatModeModel(
        remoteAgentChatModel,
        remoteAgentConversationModel,
        remoteAgentsModel,
      );

      await tick();

      // Try to set to chat mode
      await remoteAgentChatModeModel.handleSetToThreadType("chat");

      // Should remain in remote agent mode
      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });

  describe("setToRemoteAgent", () => {
    test("should set to remote agent mode with no agent", () => {
      chatModeModel.setToRemoteAgent();

      expect(remoteAgentsModel.isActive).toBe(true);
    });

    test("should set to remote agent mode with specific agent", async () => {
      const setCurrentAgentSpy = vi.spyOn(remoteAgentsModel, "setCurrentAgent");
      const agentId = "test-agent-123";

      chatModeModel.setToRemoteAgent(agentId);

      expect(remoteAgentsModel.isActive).toBe(true);
      expect(setCurrentAgentSpy).toHaveBeenCalledWith(agentId);
    });

    test("should set to remote agent mode with null", async () => {
      const clearCurrentAgentSpy = vi.spyOn(remoteAgentsModel, "clearCurrentAgent");

      // setToRemoteAgent with null calls clearCurrentAgent
      chatModeModel.setToRemoteAgent(null);

      expect(remoteAgentsModel.isActive).toBe(true);
      expect(clearCurrentAgentSpy).toHaveBeenCalled();
    });
  });

  describe("setCurrentConversation behavior", () => {
    test("should call setCurrentConversation for chat and localAgent modes but never for remoteAgent", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      // Mock no existing conversations to force creation of new threads
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([]);

      // Test chat mode - should call setCurrentConversation
      setCurrentConversationSpy.mockClear();
      await chatModeModel.handleSetToThreadType("chat");
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      // Test localAgent mode - should call setCurrentConversation
      setCurrentConversationSpy.mockClear();
      await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.manual);
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      setCurrentConversationSpy.mockClear();
      await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.auto);
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      // Test remoteAgent mode - should NOT call setCurrentConversation
      setCurrentConversationSpy.mockClear();
      await chatModeModel.handleSetToThreadType("remoteAgent");
      expect(setCurrentConversationSpy).not.toHaveBeenCalled();

      // Test switchToThread for different modes
      setCurrentConversationSpy.mockClear();
      chatModeModel.switchToThread("chat", "test-chat-id");
      expect(setCurrentConversationSpy).toHaveBeenCalledWith("test-chat-id", true, {
        noopIfSameConversation: true,
      });

      setCurrentConversationSpy.mockClear();
      chatModeModel.switchToThread("localAgent", "test-agent-id", AgentExecutionMode.manual);
      expect(setCurrentConversationSpy).toHaveBeenCalledWith("test-agent-id", true, {
        noopIfSameConversation: true,
      });

      setCurrentConversationSpy.mockClear();
      chatModeModel.switchToThread("remoteAgent", "test-remote-agent-id");
      expect(setCurrentConversationSpy).not.toHaveBeenCalled();
    });
  });

  describe("switchToThread", () => {
    test("should switch to chat thread", () => {
      const threadId = "chat-thread-123";
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      chatModeModel.switchToThread("chat", threadId);

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(threadId, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      expect(remoteAgentsModel.isActive).toBe(false);
    });

    test("should switch to local agent thread with mode", async () => {
      const threadId = "agent-thread-456";
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");
      setCurrentConversationSpy.mockResolvedValue(undefined);

      // Mock the conversation to have the proper agent metadata
      chatModel.currentConversationModel.extraData = {
        ...chatModel.currentConversationModel.extraData,
        isAgentConversation: true,
      };

      chatModeModel.switchToThread("localAgent", threadId, AgentExecutionMode.auto);
      await tick();
      await tick();

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(threadId, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
    });

    test("should switch to remote agent thread", () => {
      const threadId = "remote-thread-789";
      const setToRemoteAgentSpy = vi.spyOn(chatModeModel, "setToRemoteAgent");

      chatModeModel.switchToThread("remoteAgent", threadId);

      // For remote agent threads, setCurrentConversation is not called
      // Instead, setToRemoteAgent is called with the threadId
      expect(setToRemoteAgentSpy).toHaveBeenCalledWith(threadId);
      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });

  describe("handleMessageFromExtension", () => {
    test("should handle remoteAgentSelectAgentId message", () => {
      const setToRemoteAgentSpy = vi.spyOn(chatModeModel, "setToRemoteAgent");
      const agentId = "selected-agent-123";

      const event = {
        data: {
          type: WebViewMessageType.remoteAgentSelectAgentId,
          data: {
            agentId,
          },
        },
      } as MessageEvent;

      const handled = chatModeModel.handleMessageFromExtension(event);

      expect(handled).toBe(true);
      expect(setToRemoteAgentSpy).toHaveBeenCalledWith(agentId);
    });

    test("should not handle other message types", () => {
      const event = {
        data: {
          type: WebViewMessageType.chatClearMetadata,
        },
      } as MessageEvent;

      const handled = chatModeModel.handleMessageFromExtension(event);

      expect(handled).toBe(false);
    });
  });

  describe("findLatestChatThread", () => {
    test("should find latest chat thread", () => {
      // Mock orderedConversations as a method
      // The method returns the first matching item, not the last
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
        { id: "agent-1", extraData: { isAgentConversation: true } },
        { id: "chat-2", extraData: { isAgentConversation: false } },
        { id: "chat-3", extraData: {} }, // No isAgentConversation means it's a chat
      ] as any);

      const result = chatModeModel["findLatestThread"]("chat");

      // Returns the first chat thread found
      expect(result).toBe("chat-1");
    });

    test("should return undefined when no chat threads exist", () => {
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "agent-1", extraData: { isAgentConversation: true } },
        { id: "agent-2", extraData: { isAgentConversation: true } },
      ] as any);

      const result = chatModeModel["findLatestThread"]("chat");

      expect(result).toBeUndefined();
    });
  });

  describe("findLatestLocalAgentThread", () => {
    test("should find latest local agent thread", () => {
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
        { id: "agent-1", extraData: { isAgentConversation: true } },
        { id: "agent-2", extraData: { isAgentConversation: true } },
      ] as any);

      const result = chatModeModel["findLatestThread"]("localAgent");

      // Returns the first agent thread found
      expect(result).toBe("agent-1");
    });

    test("should return undefined when no agent threads exist", () => {
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
        { id: "chat-2", extraData: {} },
      ] as any);

      const result = chatModeModel["findLatestThread"]("localAgent");

      expect(result).toBeUndefined();
    });
  });

  describe("handleSetToThreadType", () => {
    describe("chat mode", () => {
      test("should switch to latest chat thread if exists", async () => {
        const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");
        vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
          { id: "chat-1", extraData: { isAgentConversation: false } },
        ] as any);

        await chatModeModel.handleSetToThreadType("chat");

        expect(switchToThreadSpy).toHaveBeenCalledWith("chat", "chat-1", undefined);
      });

      test("should create new chat thread if none exist", async () => {
        const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");
        vi.spyOn(chatModel, "orderedConversations").mockReturnValue([]);

        await chatModeModel.handleSetToThreadType("chat");

        expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
          noopIfSameConversation: true,
        });
        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      });
    });

    describe("local agent mode", () => {
      test("should switch to latest agent thread if exists", async () => {
        const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");
        vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
          { id: "agent-1", extraData: { isAgentConversation: true } },
        ] as any);

        await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.manual);

        expect(switchToThreadSpy).toHaveBeenCalledWith(
          "localAgent",
          "agent-1",
          AgentExecutionMode.manual,
        );
      });

      test("should create new agent thread if none exist", async () => {
        const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");
        vi.spyOn(chatModel, "orderedConversations").mockReturnValue([]);

        await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.auto);

        expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
          noopIfSameConversation: true,
        });
        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
      });
    });

    describe("remote agent mode", () => {
      test("should handle set to remote agent when no existing thread", () => {
        const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");

        // Mock no existing remote agents
        vi.spyOn(remoteAgentsModel, "currentAgent", "get").mockReturnValue(undefined);
        vi.spyOn(remoteAgentsModel, "agentOverviews", "get").mockReturnValue([]);

        chatModeModel.handleSetToThreadType("remoteAgent");

        // When no remote agent thread exists, it creates a new one (activates remote agents)
        expect(remoteAgentsModel.isActive).toBe(true);
        expect(switchToThreadSpy).not.toHaveBeenCalled();
      });

      test("should switch to existing remote agent thread", () => {
        const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");

        // Mock existing remote agent
        const mockAgentId = "existing-agent-123";
        vi.spyOn(remoteAgentsModel, "currentAgent", "get").mockReturnValue({
          // eslint-disable-next-line @typescript-eslint/naming-convention
          remote_agent_id: mockAgentId,
        } as any);

        chatModeModel.handleSetToThreadType("remoteAgent");

        // When a remote agent thread exists, it switches to it
        expect(switchToThreadSpy).toHaveBeenCalledWith("remoteAgent", mockAgentId, undefined);
      });
    });
  });

  describe("Mode Selector Event Reporting", () => {
    test("should report mode selector open event", async () => {
      const reportSpy = vi.spyOn(remoteAgentsModel, "reportChatModeEvent");

      chatModeModel.reportModeSelectorOpen();

      // Wait for async message to be sent
      await tick();

      expect(reportSpy).toHaveBeenCalledWith(ModeSelectorAction.open, ModeSelectorMode.chat);
    });

    test("should report mode selector close event", async () => {
      const reportSpy = vi.spyOn(remoteAgentsModel, "reportChatModeEvent");

      chatModeModel.reportModeSelectorClose();

      // Wait for async message to be sent
      await tick();

      expect(reportSpy).toHaveBeenCalledWith(ModeSelectorAction.close, ModeSelectorMode.chat);
    });

    test("should report mode selector init event", async () => {
      const reportSpy = vi.spyOn(remoteAgentsModel, "reportChatModeEvent");

      chatModeModel.reportModeSelectorInit();

      // Wait for async message to be sent
      await tick();

      expect(reportSpy).toHaveBeenCalledWith(ModeSelectorAction.init, ModeSelectorMode.chat);
    });

    test("should include current mode in open/close events", async () => {
      const reportSpy = vi.spyOn(remoteAgentsModel, "reportChatModeEvent");

      // Set to agent mode first
      await chatModeModel.handleSetToThreadType("localAgent", AgentExecutionMode.manual);
      await tick();

      // Clear previous calls
      reportSpy.mockClear();

      chatModeModel.reportModeSelectorOpen();

      // Wait for async message to be sent
      await tick();

      expect(reportSpy).toHaveBeenCalledWith(ModeSelectorAction.open, ModeSelectorMode.agent);
    });

    test("should include current mode in init event", async () => {
      const reportSpy = vi.spyOn(remoteAgentsModel, "reportChatModeEvent");

      // Set to remote agent mode first
      remoteAgentsModel.setIsActive(true);
      await tick();

      // Clear previous calls
      reportSpy.mockClear();

      chatModeModel.reportModeSelectorInit();

      // Wait for async message to be sent
      await tick();

      expect(reportSpy).toHaveBeenCalledWith(ModeSelectorAction.init, ModeSelectorMode.remoteAgent);
    });

    test("should handle errors in event reporting gracefully", async () => {
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      const reportSpy = vi
        .spyOn(remoteAgentsModel, "reportChatModeEvent")
        .mockRejectedValue(new Error("Network error"));

      chatModeModel.reportModeSelectorOpen();

      // Wait for async message to be sent and error to be caught
      await tick();

      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to report chat mode event:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
      reportSpy.mockRestore();
    });
  });

  describe("Mode switching after creating new thread", () => {
    test("should switch to latest existing thread after creating new thread", async () => {
      // Create some existing conversations
      const chatConversation = {
        id: "existing-chat-123",
        messages: [{ id: "msg1", content: "Hello" }],
        extraData: { isAgentConversation: false },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [],
        feedbackStates: {},
        requestIds: [],
      };
      const agentConversation = {
        id: "existing-agent-456",
        messages: [{ id: "msg2", content: "Agent message" }],
        extraData: { isAgentConversation: true },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [],
        feedbackStates: {},
        requestIds: [],
      };

      // Mock orderedConversations to return our test conversations
      const orderedConversationsSpy = vi
        .spyOn(chatModel, "orderedConversations")
        .mockReturnValue([chatConversation, agentConversation]);

      // Mock setCurrentConversation to track calls
      const setCurrentConversationSpy = vi
        .spyOn(chatModel, "setCurrentConversation")
        .mockResolvedValue();

      // Mock switchToThread to track calls
      const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");

      // Step 1: Switch to chat mode - should find and switch to existing chat thread
      await chatModeModel.handleSetToChat();
      expect(switchToThreadSpy).toHaveBeenCalledWith("chat", "existing-chat-123", undefined);

      // Step 2: Create a new thread (simulating clicking "New" button)
      switchToThreadSpy.mockClear();
      setCurrentConversationSpy.mockClear();

      // Mock currentConversationId to return NEW_AGENT_KEY (simulating new thread creation)
      Object.defineProperty(chatModel, "currentConversationId", {
        get: () => "__NEW_AGENT__",
        configurable: true,
      });

      await chatModeModel.createThreadOfCurrentType();
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      // Step 3: Switch to agent mode - should switch to existing agent thread, NOT create new one
      switchToThreadSpy.mockClear();
      setCurrentConversationSpy.mockClear();

      await chatModeModel.handleSetToAgent();

      // Should call switchToThread with existing agent thread, not create new thread
      expect(switchToThreadSpy).toHaveBeenCalledWith("localAgent", "existing-agent-456", undefined);
      expect(setCurrentConversationSpy).not.toHaveBeenCalledWith(
        undefined,
        true,
        expect.any(Object),
      );

      // Clean up
      orderedConversationsSpy.mockRestore();
      setCurrentConversationSpy.mockRestore();
      switchToThreadSpy.mockRestore();
    });
  });

  describe("selectedModelId inheritance", () => {
    test("when model registry is disabled, selectedModelId is not readable and does not get inherited for new chat thread", async () => {
      // Set a selectedModelId on the current conversation
      const testModelId = "test-model-123";
      chatModel.currentConversationModel.setSelectedModelId(testModelId);

      // With flag off, the getter should be undefined even after setting
      expect(chatModel.flags.enableModelRegistry).toBe(false);
      expect(chatModel.currentConversationModel.selectedModelId).toBeUndefined();

      // Create a new chat thread
      await chatModeModel.createNewThread("chat");

      // Still undefined on the new conversation
      expect(chatModel.currentConversationModel.selectedModelId).toBeUndefined();
    });

    test("when model registry is disabled, selectedModelId is not readable and does not get inherited for new localAgent thread", async () => {
      // Set a selectedModelId on the current conversation
      const testModelId = "test-model-456";
      chatModel.currentConversationModel.setSelectedModelId(testModelId);

      // With flag off, the getter should be undefined even after setting
      expect(chatModel.flags.enableModelRegistry).toBe(false);
      expect(chatModel.currentConversationModel.selectedModelId).toBeUndefined();

      // Create a new local agent thread
      await chatModeModel.createNewThread("localAgent", AgentExecutionMode.manual);

      // Still undefined on the new conversation
      expect(chatModel.currentConversationModel.selectedModelId).toBeUndefined();
    });

    test("should not inherit selectedModelId when creating new chat thread even if feature flag is on", async () => {
      // Create a new chat model with enableModelRegistry flag enabled
      const enabledChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
          enableModelRegistry: true,
        },
      });

      const enabledToolsModel = new ToolsWebviewModel(
        enabledChatModel.currentConversationModel,
        enabledChatModel.extensionClient,
        enabledChatModel,
        mockSoundModel,
        undefined,
      );

      const enabledAgentConversationModel = new AgentConversationModel(
        enabledChatModel,
        enabledToolsModel,
        mockCheckpointStore,
        mockSoundModel,
      );

      const enabledChatModeModel = new ChatModeModel(
        enabledChatModel,
        enabledAgentConversationModel,
        remoteAgentsModel,
      );

      // Set a selectedModelId on the current conversation
      const testModelId = "test-model-enabled-123";
      enabledChatModel.currentConversationModel.setSelectedModelId(testModelId);

      // Verify the current conversation has the model ID set
      expect(enabledChatModel.currentConversationModel.selectedModelId).toBe(testModelId);

      // Verify the enableModelRegistry flag is on
      expect(enabledChatModel.flags.enableModelRegistry).toBe(true);

      // Create a new chat thread
      await enabledChatModeModel.createNewThread("chat");

      // Verify the new conversation did NOT inherit the selectedModelId (chat threads don't inherit)
      expect(enabledChatModel.currentConversationModel.selectedModelId).toBe(undefined);
    });

    test("should inherit selectedModelId when creating new localAgent thread if feature flag is on", async () => {
      // Create a new chat model with enableModelRegistry flag enabled
      const enabledChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
          enableModelRegistry: true,
        },
      });

      const enabledToolsModel = new ToolsWebviewModel(
        enabledChatModel.currentConversationModel,
        enabledChatModel.extensionClient,
        enabledChatModel,
        mockSoundModel,
        undefined,
      );

      const enabledAgentConversationModel = new AgentConversationModel(
        enabledChatModel,
        enabledToolsModel,
        mockCheckpointStore,
        mockSoundModel,
      );

      const enabledChatModeModel = new ChatModeModel(
        enabledChatModel,
        enabledAgentConversationModel,
        remoteAgentsModel,
      );

      // Set a selectedModelId on the current conversation
      const testModelId = "test-model-enabled-456";
      enabledChatModel.currentConversationModel.setSelectedModelId(testModelId);

      // Verify the current conversation has the model ID set
      expect(enabledChatModel.currentConversationModel.selectedModelId).toBe(testModelId);

      // Verify the enableModelRegistry flag is on
      expect(enabledChatModel.flags.enableModelRegistry).toBe(true);

      // Create a new local agent thread
      await enabledChatModeModel.createNewThread("localAgent", AgentExecutionMode.manual);

      // Verify the new conversation inherited the selectedModelId
      expect(enabledChatModel.currentConversationModel.selectedModelId).toBe(testModelId);
    });

    test("when model registry is disabled, selectedModelId is not readable in remoteAgent mode", async () => {
      // Set a selectedModelId on the current conversation
      const testModelId = "test-model-789";
      chatModel.currentConversationModel.setSelectedModelId(testModelId);

      // With flag off, the getter should be undefined even after setting
      expect(chatModel.currentConversationModel.selectedModelId).toBeUndefined();

      // Create a new remote agent thread (this doesn't create a new conversation)
      await chatModeModel.createNewThread("remoteAgent");

      // Still undefined and remote agents should be active
      expect(chatModel.currentConversationModel.selectedModelId).toBeUndefined();
      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });
});
