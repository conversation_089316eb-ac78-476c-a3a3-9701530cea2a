# Guide: Adding a New Feature Flag to Sidecar

This guide explains the process of adding a new feature flag to the Sidecar system. Feature flags allow for enabling/disabling features or configuring behavior without code changes.

## Overview

When adding a new feature flag, you need to update several files across the codebase to ensure the flag is properly defined, initialized, and accessible throughout the system. This guide uses the example of adding a feature flag called `agentEditToolFuzzyMatchSuccessMessage` of type string.
Make sure to replace `agentEditToolFuzzyMatchSuccessMessage` with your actual flag name and `string` with your actual flag type.

## Important Proto Field Ordering Rule

**CRITICAL**: When adding new fields to proto files (both `sidecarrpc.proto` and `public_api.proto`), always add them at the end of the message with the next available field number. Never insert fields in the middle or reuse field numbers. This ensures backward compatibility and prevents issues with existing deployments.

## Step 1: Update the SidecarFlags Interface

First, add the new flag to the `SidecarFlags` interface in `clients/sidecar/libs/src/client-interfaces/feature-flags.ts`:

```typescript
export type SidecarFlags = {
  // Existing flags...
  agentEditToolSchemaType: string;
  agentEditToolFuzzyMatchSuccessMessage: string; // Add your new flag here
  // Other flags...
};
```

## Step 2: Update Mock Feature Flags for Testing

Update the mock feature flags in `clients/sidecar/libs/src/__tests__/mocks/mock-client-feature-flags.ts`:

```typescript
public flags: SidecarFlags = {
  // Existing flags...
  agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
  agentEditToolFuzzyMatchSuccessMessage: "Your default value here", // Add your new flag with a default value
  // Other flags...
};
```

## Step 3: Update the Proto File

Add the new flag to the proto file `clients/sidecar/node-process/protos/sidecarrpc.proto`:

**IMPORTANT: Always add new fields at the end of the message with the next available field number. Never insert fields in the middle or reuse field numbers.**

```protobuf
message SidecarFlags {
  // Existing fields...
  string agentEditToolSchemaType = 6;
  // ... other existing fields ...
  bool agentSaveFileToolInstructionsReminder = 14;
  string agentEditToolFuzzyMatchSuccessMessage = 15; // Add your new flag at the END with the next available field number
}
```

## Step 4: Update Client Feature Flags Implementation

Update the client feature flags implementation in `clients/sidecar/node-process/src/client-interfaces/client-feature-flags.ts`:

```typescript
// In the _flags initialization
private _flags = new SidecarFlagsProto({
  // Existing flags...
  agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
  agentEditToolFuzzyMatchSuccessMessage: "Your default value here", // Add your new flag with a default value
  // Other flags...
});

// In the flags getter
public get flags(): ISideCarFlags {
  return cloneDeep({
    // Existing flags...
    agentEditToolSchemaType: this._flags.agentEditToolSchemaType,
    agentEditToolFuzzyMatchSuccessMessage: this._flags.agentEditToolFuzzyMatchSuccessMessage, // Add your new flag
    // Other flags...
  });
}
```

## Step 5: Update VSCode Feature Flags Files

### 5.1: Update VSCode Feature Flags Interface

Add the new flag to the VSCode feature flags interface in `clients/vscode/src/feature-flags.ts`:

```typescript
// In the interface
agentEditToolSchemaType: string;
agentEditToolFuzzyMatchSuccessMessage: string; // Add your new flag
// Other flags...

// In the default feature flags
agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
agentEditToolFuzzyMatchSuccessMessage: "Your default value here", // Add your new flag with a default value
// Other flags...
```

### 5.2: Update VSCode Feature Flags Tests

Add the new flag to the feature flags test file in `clients/vscode/src/__tests__/feature-flags.test.ts`:

```typescript
// In the newFlags object in the change-flag test
agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
agentEditToolFuzzyMatchSuccessMessage: "Your default value here", // Add your new flag with a default value
// Other flags...
```

### 5.3: Update VSCode Client Feature Flags Implementation

Add the new flag to the client feature flags implementation in `clients/vscode/src/client-interfaces/client-feature-flags.ts`:

```typescript
// In the flags getter
agentEditToolSchemaType:
    this._featureFlagManager.currentFlags.agentEditToolSchemaType ??
    "StrReplaceEditorToolDefinitionNested",
agentEditToolFuzzyMatchSuccessMessage:
    this._featureFlagManager.currentFlags.agentEditToolFuzzyMatchSuccessMessage ??
    "Your default value here", // Add your new flag with a default value
// Other flags...
```

## Step 6: Update Public API Proto File

Add the new flag to the public API proto file in `services/api_proxy/public_api.proto`:

**IMPORTANT: Always add new fields at the end of the FeatureFlags message with the next available field number. Never insert fields in the middle or reuse field numbers.**

```protobuf
// In the FeatureFlags message
optional string agent_edit_tool_schema_type = 90;
// ... other existing fields ...
optional string vscode_task_list_min_version = 120;

// Custom message to display when fuzzy matching is used in the str-replace-editor-tool
optional string agent_edit_tool_fuzzy_match_success_message = 121; // Add your new flag at the END with the next available field number

// Other fields...
```

## Step 7: Update Feature Flags Handler in Rust

Update the feature flags handler in `services/api_proxy/server/src/handlers.rs`:

```rust
// In the feature flags handling code
agent_edit_tool_schema_type: feature_flags.lookup(
    "agent_edit_tool_schema_type",
    "StrReplaceEditorToolDefinitionNested",
),
agent_edit_tool_fuzzy_match_success_message: feature_flags.lookup(
    "agent_edit_tool_fuzzy_match_success_message",
    "Your default value here",
), // Add your new flag with a default value
// Other flags...

// In the test case
agent_edit_tool_schema_type: Some(
    "StrReplaceEditorToolDefinitionNested".to_string(),
),
agent_edit_tool_fuzzy_match_success_message: Some(
    "Your default value here".to_string(),
), // Add your new flag with a default value
// Other flags...
```

## Step 8: Update VSCode Augment API

Update the VSCode augment-api.ts file to handle the new flag:

```typescript
// In the BackFeatureFlags interface
export type BackFeatureFlags = {
    // Existing flags...
    agent_edit_tool_schema_type?: string;
    agent_edit_tool_fuzzy_match_success_message?: string; // Add your new flag
    // Other flags...
}

// In the feature flags handling code
if (resp.feature_flags.agent_edit_tool_schema_type !== undefined) {
    featureFlags.agentEditToolSchemaType =
        resp.feature_flags.agent_edit_tool_schema_type;
}
if (resp.feature_flags.agent_edit_tool_fuzzy_match_success_message !== undefined) {
    featureFlags.agentEditToolFuzzyMatchSuccessMessage =
        resp.feature_flags.agent_edit_tool_fuzzy_match_success_message;
} // Add your new flag handling
```

## Step 9: Update Beachhead Client (if applicable)

If your feature flag is used in the beachhead client, you need to update three files:

### 9.1: Update Beachhead Feature Flags Interface

Add the new flag to the FeatureFlags interface in `clients/beachhead/src/feature-flags.ts`:

```typescript
export interface FeatureFlags {
    // Existing flags...
    agentEditToolSchemaType: string;
    agentEditToolFuzzyMatchSuccessMessage: string; // Add your new flag here
    // Other flags...
}

export const defaultFeatureFlags: FeatureFlags = {
    // Existing flags...
    agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
    agentEditToolFuzzyMatchSuccessMessage: "Your default value here", // Add your new flag with a default value
    // Other flags...
};
```

### 9.2: Update Beachhead API Types

Add the new flag to the BackFeatureFlags interface in `clients/beachhead/src/augment-api.ts`:

```typescript
export type BackFeatureFlags = {
    // Existing flags...
    agent_edit_tool_schema_type?: string;
    agent_edit_tool_fuzzy_match_success_message?: string; // Add your new flag
    // Other flags...
};
```

### 9.3: Update Beachhead Feature Flag Conversion

Update the feature flag conversion logic in `clients/beachhead/src/index.ts`:

```typescript
// In the convertToFeatureFlags function
const featureFlags: FeatureFlags = {
    ...defaultFeatureFlags,
    // Existing flags...
    agentEditToolSchemaType:
        apiFeatureFlags.agentEditToolSchemaType ?? defaultFeatureFlags.agentEditToolSchemaType,
    agentEditToolFuzzyMatchSuccessMessage:
        apiFeatureFlags.agentEditToolFuzzyMatchSuccessMessage ??
        defaultFeatureFlags.agentEditToolFuzzyMatchSuccessMessage, // Add your new flag
    // Other flags...
};

// In the createClientFeatureFlags function
return {
    flags: {
        // Existing flags...
        agentEditToolSchemaType: featureFlags.agentEditToolSchemaType,
        agentEditToolFuzzyMatchSuccessMessage: featureFlags.agentEditToolFuzzyMatchSuccessMessage, // Add your new flag
        // Other flags...
    },
};
```

## Step 10: Update Feature Flags Configuration in Jsonnet

Add the new feature flag to the `tools/feature_flags/flags.jsonnet` file:

```jsonnet
agent_edit_tool_fuzzy_match_success_message: {
  sync: true,
  description: 'The success message to display when fuzzy matching is used in the str-replace-editor-tool.',
  default_return_value: 'Replacement successful. old_str and new_str were slightly modified to match the original file content.',
  envs: {
    production: {
      rules: [
        {
          namespace: [
            'staging-shard-0',
          ],
          return_value: 'Replacement successful. old_str and new_str were slightly modified to match the original file content.',
        },
        {
          return_value: 'Replacement successful. old_str and new_str were slightly modified to match the original file content.',
        },
      ],
    },
  },
},
```

This configuration defines:
- `sync: true` - Indicates this flag should be synchronized across clients
- `description` - A human-readable description of the flag's purpose
- `default_return_value` - The default value for the flag
- `envs.production.rules` - Rules for different environments and namespaces

## Step 11: Update IntelliJ Feature Flags

### 11.1: Update IntelliJ FeatureFlags.kt

Add the new flag to the FeatureFlags data class in `clients/intellij/src/main/kotlin/com/augmentcode/intellij/featureflags/FeatureFlags.kt`:

```kotlin
data class FeatureFlags(
  // Existing flags...
  val agentViewToolParams: String,
  val nonDismissibleBannerTestTreatment: String,
  val agentEditToolFuzzyMatchSuccessMessage: String, // Add your new flag here
)
```

### 11.2: Update the copyWithOverrides method

Add the override logic in the same file:

```kotlin
// In the copyWithOverrides method
agentEditToolFuzzyMatchSuccessMessage =
  if (flagsFromAPI?.hasAgentEditToolFuzzyMatchSuccessMessage() == true) {
    flagsFromAPI.agentEditToolFuzzyMatchSuccessMessage
  } else {
    agentEditToolFuzzyMatchSuccessMessage
  },
```

### 11.3: Update IntelliJ DefaultFeatureFlags.kt

Add the default value in `clients/intellij/src/main/kotlin/com/augmentcode/intellij/featureflags/DefaultFeatureFlags.kt`:

```kotlin
val DefaultFeatureFlags =
  FeatureFlags(
    // Existing flags...
    nonDismissibleBannerTestTreatment = "off",
    agentEditToolFuzzyMatchSuccessMessage = "Your default value here", // Add your new flag with a default value
    // Other flags...
  )
```

### 11.4: Update IntelliJ SidecarService

Add the new flag to the sidecar configuration in `clients/intellij/src/main/kotlin/com/augmentcode/intellij/sidecar/SidecarService.kt`:

```kotlin
// In the sidecar configuration builder
.setAgentViewToolParams(flagsSnapshot.agentViewToolParams)
.setAgentEditToolFuzzyMatchSuccessMessage(flagsSnapshot.agentEditToolFuzzyMatchSuccessMessage) // Add your new flag
.build(),
```

## Step 12: Regenerate TypeScript Files from Proto

After updating the proto file, regenerate the TypeScript files:

```bash
cd /home/<USER>/augment/clients/sidecar/node-process
bazel build //clients/sidecar/node-process/protos:sidecar_ts_protos
```

## Step 13: Use the Feature Flag in Your Code

Now you can use the feature flag in your code:

```typescript
import { getClientFeatureFlags } from "../../client-interfaces/feature-flags";

// ...

const myFeatureFlag = getClientFeatureFlags().flags.agentEditToolFuzzyMatchSuccessMessage;
```

## Conclusion

By following these steps, you've successfully added a new feature flag to the Sidecar system. This allows for configuring behavior without code changes and enables easier testing and deployment of new features.

## Summary Checklist

Here's a quick checklist of all the files you need to modify when adding a new feature flag:

1. ✅ `clients/sidecar/libs/src/client-interfaces/feature-flags.ts` - Add to SidecarFlags interface
2. ✅ `clients/sidecar/libs/src/__tests__/mocks/mock-client-feature-flags.ts` - Add to mock flags
3. ✅ `clients/sidecar/node-process/protos/sidecarrpc.proto` - Add to SidecarFlags message
4. ✅ `clients/sidecar/node-process/src/client-interfaces/client-feature-flags.ts` - Add to _flags and flags getter
5. ✅ `clients/vscode/src/feature-flags.ts` - Add to interface and default flags
6. ✅ `clients/vscode/src/__tests__/feature-flags.test.ts` - Add to test flags
7. ✅ `clients/vscode/src/client-interfaces/client-feature-flags.ts` - Add to flags getter
8. ✅ `services/api_proxy/public_api.proto` - Add to FeatureFlags message
9. ✅ `services/api_proxy/server/src/handlers.rs` - Add to feature flags handler
10. ✅ `clients/vscode/src/augment-api.ts` - Add to BackFeatureFlags interface and handling code
11. ✅ `clients/beachhead/src/feature-flags.ts` - Add to FeatureFlags interface and defaultFeatureFlags (if applicable)
12. ✅ `clients/beachhead/src/augment-api.ts` - Add to BackFeatureFlags interface (if applicable)
13. ✅ `clients/beachhead/src/index.ts` - Add to feature flag conversion logic (if applicable)
14. ✅ `tools/feature_flags/flags.jsonnet` - Add feature flag configuration
15. ✅ `clients/intellij/src/main/java/com/augmentcode/api/FeatureFlags.java` - Add to FeatureFlags class
16. ✅ `clients/intellij/src/main/kotlin/com/augmentcode/intellij/settings/FeatureFlagManager.kt` - Add method to interface
17. ✅ `clients/intellij/src/main/kotlin/com/augmentcode/intellij/settings/FeatureFlagManagerImpl.kt` - Add implementation
18. ✅ `clients/intellij/src/main/kotlin/com/augmentcode/intellij/sidecar/SidecarService.kt` - Add to sidecar configuration
19. ✅ Regenerate TypeScript files from proto with `pnpm run build-protos`
