import {
  SidecarAnalytics,
  AnalyticsContext,
} from "../analytics/segment-analytics";
import { ChatModeType } from "../chat";

let _analytics: SidecarAnalytics | undefined;

export interface InitializeAnalyticsCommand {
  writeKey: string;
  context: AnalyticsContext;
  anonymousId: string;
}

export function initializeAnalytics(command: InitializeAnalyticsCommand): void {
  if (_analytics) {
    void _analytics.dispose();
  }

  _analytics = new SidecarAnalytics(command.writeKey);
  _analytics.initialize(command.context, command.anonymousId);
}

export async function disposeAnalytics(): Promise<void> {
  if (_analytics) {
    await _analytics.dispose();
    _analytics = undefined;
  }
}

export function trackEvent(
  eventName: string,
  properties?: Record<string, any>,
): void {
  _analytics?.trackEvent(eventName, properties);
}

export function identifyUser(
  userId: string,
  traits?: Record<string, any>,
): void {
  _analytics?.identifyUser(userId, traits);
}

// Analytics event names constants
export const ANALYTICS_EVENTS = {
  EXPERIMENT_VIEWED: "experiment_viewed",
  THREAD_CREATION_ATTEMPTED: "thread_creation_attempted",
  SEND_ACTION_TRIGGERED: "send_action_triggered",
  CANCEL_ACTION_TRIGGERED: "cancel_action_triggered",
  RESEND_ACTION_TRIGGERED: "resend_action_triggered",
  AGENT_EXECUTION_MODE_TOGGLED: "agent_execution_mode_toggled",
  MESSAGE_SEND_ERROR_DISPLAYED: "message_send_error_displayed",
  MESSAGE_SEND_RETRY_CLICKED: "message_send_retry_clicked",
  // Performance metrics
  MESSAGE_SEND_TIMING: "message_sent_timing",
  VSCODE_EXTENSION_STARTUP: "vscode_extension_started_up",
  // Notifications
  NOTIFICATION_DISPLAYED: "notification_displayed",
  NOTIFICATION_DISMISSED: "notification_dismissed",
  SUBSCRIPTION_WARNING_UPGRADE_CLICKED: "subscription_warning_upgrade_clicked",
} as const;

// Centralized experiment names
export const EXPERIMENTS = {
  NON_DISMISSIBLE_SUBSCRIPTION_BANNER: "non_dismissible_subscription_banner",
} as const;
export type ExperimentName = (typeof EXPERIMENTS)[keyof typeof EXPERIMENTS];

// Centralized experiment treatment variants for analytics
export const EXPERIMENT_TREATMENTS = {
  V0: "v0",
  CONTROL: "control",
  OFF: "off",
} as const;
export type ExperimentTreatment =
  (typeof EXPERIMENT_TREATMENTS)[keyof typeof EXPERIMENT_TREATMENTS];

export type AnalyticsEventName =
  (typeof ANALYTICS_EVENTS)[keyof typeof ANALYTICS_EVENTS];

interface BaseChatSourceEventProperties {
  chatMode: ChatModeType;
  source: "keybinding" | "button";
  agentExecutionMode?: "auto" | "manual";
}
// Event property type definitions
export interface ThreadCreationAttemptedEventProperties
  extends BaseChatSourceEventProperties {}

export interface SendActionTriggeredEventProperties
  extends BaseChatSourceEventProperties {
  sendMode?: "send" | "addTask";
}

export interface CancelActionTriggeredEventProperties
  extends BaseChatSourceEventProperties {}

export interface ResendActionTriggeredEventProperties
  extends BaseChatSourceEventProperties {}

interface BaseMessageSendErrorEventProperties
  extends BaseChatSourceEventProperties {
  errorMessagePreview?: string; // First 100 chars of display_error_message if present
  requestId?: string;
}
export interface MessageSendErrorDisplayedEventProperties
  extends BaseMessageSendErrorEventProperties {}

export interface MessageSendRetryClickedEventProperties
  extends BaseMessageSendErrorEventProperties {}

export interface AgentExecutionModeToggledEventProperties {
  source: "button" | "keybinding";
  newMode: "auto" | "manual";
}

export interface SubscriptionWarningUpgradeClickedEventProperties
  extends BaseChatSourceEventProperties {
  reason: "inactive" | "usage_depleted" | "expiring";
  daysRemaining?: number;
  treatment?: ExperimentTreatment;
}

// Performance metrics event properties
export interface ChatInitialLoadToRenderEventProperties {
  chatMode: ChatModeType;
  loadTimeMs: number;
  hadInitialConversation: boolean;
  agentExecutionMode?: "auto" | "manual";
}

export interface MessageSendTimingProperties {
  requestId: string;
  timeToFirstTokenMs: number;
  timeToLastTokenMs: number;
  chatMode: ChatModeType;
  agentExecutionMode?: "auto" | "manual";
  sendMode?: "send" | "addTask";
  modelId?: string;
  responseLength?: number;
  chatHistoryLength?: number;
  errorType?: string;
}

export interface VSCodeExtensionStartupProperties {
  startupDurationMs: number;
  startupSuccess: boolean;
  vscodeVersion: string;
  extensionVersion: string;
  extensionMode: string;
  startupError?: string;
  phaseOauthAuthMs?: number;
  phaseGetModelConfigMs?: number;
  phaseWorkspaceManagerInitMs?: number;
}

export interface NotificationDisplayedEventProperties {
  notificationId: string;
  notificationLevel: "INFO" | "WARNING" | "ERROR" | "UNSPECIFIED";
  notificationType: "toast" | "banner";
}

export interface NotificationDismissedEventProperties {
  notificationId: string;
  notificationLevel: "INFO" | "WARNING" | "ERROR" | "UNSPECIFIED";
  notificationType: "toast" | "banner";
  actionItemTitle?: string;
}

export interface ExperimentViewedEventProperties {
  experimentName: string;
  treatment: ExperimentTreatment;
}

export type AnalyticsEventProperties =
  | ThreadCreationAttemptedEventProperties
  | SendActionTriggeredEventProperties
  | CancelActionTriggeredEventProperties
  | ResendActionTriggeredEventProperties
  | MessageSendErrorDisplayedEventProperties
  | MessageSendRetryClickedEventProperties
  | AgentExecutionModeToggledEventProperties
  | MessageSendTimingProperties
  | VSCodeExtensionStartupProperties
  | NotificationDisplayedEventProperties
  | NotificationDismissedEventProperties;

// Type mapping from event names to their corresponding property types
export type EventNameToPropertiesMap = {
  [ANALYTICS_EVENTS.EXPERIMENT_VIEWED]: ExperimentViewedEventProperties;
  [ANALYTICS_EVENTS.THREAD_CREATION_ATTEMPTED]: ThreadCreationAttemptedEventProperties;
  [ANALYTICS_EVENTS.SEND_ACTION_TRIGGERED]: SendActionTriggeredEventProperties;
  [ANALYTICS_EVENTS.CANCEL_ACTION_TRIGGERED]: CancelActionTriggeredEventProperties;
  [ANALYTICS_EVENTS.RESEND_ACTION_TRIGGERED]: ResendActionTriggeredEventProperties;
  [ANALYTICS_EVENTS.AGENT_EXECUTION_MODE_TOGGLED]: AgentExecutionModeToggledEventProperties;
  [ANALYTICS_EVENTS.MESSAGE_SEND_ERROR_DISPLAYED]: MessageSendErrorDisplayedEventProperties;
  [ANALYTICS_EVENTS.MESSAGE_SEND_RETRY_CLICKED]: MessageSendRetryClickedEventProperties;
  [ANALYTICS_EVENTS.MESSAGE_SEND_TIMING]: MessageSendTimingProperties;
  [ANALYTICS_EVENTS.VSCODE_EXTENSION_STARTUP]: VSCodeExtensionStartupProperties;
  [ANALYTICS_EVENTS.NOTIFICATION_DISPLAYED]: NotificationDisplayedEventProperties;
  [ANALYTICS_EVENTS.NOTIFICATION_DISMISSED]: NotificationDismissedEventProperties;
  [ANALYTICS_EVENTS.SUBSCRIPTION_WARNING_UPGRADE_CLICKED]: SubscriptionWarningUpgradeClickedEventProperties;
};

// Track experiment exposure (view)
export function trackExperimentViewed<K extends AnalyticsEventName>(
  experimentName: ExperimentName,
  treatment: ExperimentTreatment,
  properties?: EventNameToPropertiesMap[K],
): void {
  trackEventWithTypes(ANALYTICS_EVENTS.EXPERIMENT_VIEWED, {
    experimentName,
    treatment,
    ...(properties ?? {}),
  });
}

// Type-safe event tracking with proper overloads
export function trackEventWithTypes<K extends AnalyticsEventName>(
  eventName: K,
  properties: EventNameToPropertiesMap[K],
): void {
  trackEvent(eventName, properties);
}
