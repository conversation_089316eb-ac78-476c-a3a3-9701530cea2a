import {
  IClientFeatureFlags,
  SidecarFlags,
} from "../../client-interfaces/feature-flags";
import { EXPERIMENT_TREATMENTS } from "../../client-interfaces/analytics";

export class MockClientFeatureFlags implements IClientFeatureFlags {
  public flags: SidecarFlags = {
    agentEditTool: "",
    enableChatWithTools: false,
    enableAgentMode: false,
    enableAgentSwarmMode: false,
    enableSwarmMode: false,
    enableNewThreadsList: false,
    memoriesParams: {},
    agentEditToolMinViewSize: 0,
    agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
    agentEditToolEnableFuzzyMatching: false,
    agentEditToolFuzzyMatchSuccessMessage:
      "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
    agentEditToolFuzzyMatchMaxDiff: 50,
    agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
    agentEditToolInstructionsReminder: false,
    agentEditToolShowResultSnippet: true,
    agentEditToolMaxLines: 200,
    agentSaveFileToolInstructionsReminder: false,
    enableTaskList: false,
    enableSupportToolUseStart: false,
    grepSearchToolEnable: false,
    grepSearchToolTimelimitSec: 10,
    grepSearchToolOutputCharsLimit: 5000,
    grepSearchToolNumContextLines: 5,
    useHistorySummary: false,
    historySummaryParams: "",
    enableUntruncatedContentStorage: false,
    maxLinesTerminalProcessOutputAfterTruncation: 0,
    maxLinesTerminalProcessOutput: 0,
    truncationFooterAdditionText: "",
    enableCommitIndexing: false,
    maxCommitsToIndex: 0,
    enableExchangeStorage: false,
    enableToolUseStateStorage: false,
    enableAgentGitTracker: false,
    agentViewToolParams: '{"view_dir_max_entries_per_depth": [-1, 50]}',
    nonDismissibleBannerTestTreatment: EXPERIMENT_TREATMENTS.OFF,
  };

  constructor(f: Partial<SidecarFlags> = {}) {
    this.flags = { ...this.flags, ...f };
  }
}
