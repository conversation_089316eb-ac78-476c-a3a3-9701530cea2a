import {
  AgentRequestEventData,
  AgentSessionEventData,
} from "../../metrics/types";
import { IQualifiedPathName } from "../../workspace/workspace-types";
import { WebViewMessage } from "../common-webview-messages";
import { SoundSettings, SwarmModeSettings } from "../../agent/agent-edit-types";
import { ChatMode } from "../../chat/chat-types";

export enum AgentWebViewMessageType {
  getEditListRequest = "agent-get-edit-list-request",
  getEditListResponse = "agent-get-edit-list-response",
  getEditChangesByRequestIdRequest = "agent-get-edit-changes-by-request-id-request",
  getEditChangesByRequestIdResponse = "agent-get-edit-changes-by-request-id-response",
  setCurrentConversation = "agent-set-current-conversation",
  migrateConversationId = "agent-migrate-conversation-id",
  revertToTimestamp = "revert-to-timestamp",
  chatAgentEditAcceptAll = "chat-agent-edit-accept-all",
  reportAgentSessionEvent = "report-agent-session-event",
  reportAgentRequestEvent = "report-agent-request-event",
  chatReviewAgentFile = "chat-review-agent-file",
  getAgentEditContentsByRequestId = "get-agent-edit-contents-by-request-id",
  getAgentEditContentsByRequestIdResponse = "get-agent-edit-contents-by-request-id-response",

  // Agent usage tracking
  checkHasEverUsedAgent = "check-has-ever-used-agent",
  checkHasEverUsedAgentResponse = "check-has-ever-used-agent-response",
  setHasEverUsedAgent = "set-has-ever-used-agent",

  // Remote agent usage tracking
  checkHasEverUsedRemoteAgent = "check-has-ever-used-remote-agent",
  checkHasEverUsedRemoteAgentResponse = "check-has-ever-used-remote-agent-response",
  setHasEverUsedRemoteAgent = "set-has-ever-used-remote-agent",

  // Sound settings
  getSoundSettings = "get-sound-settings",
  getSoundSettingsResponse = "get-sound-settings-response",
  updateSoundSettings = "update-sound-settings",
  soundSettingsBroadcast = "sound-settings-broadcast",

  // Swarm mode settings
  getSwarmModeSettings = "get-swarm-mode-settings",
  getSwarmModeSettingsResponse = "get-swarm-mode-settings-response",
  updateSwarmModeSettings = "update-swarm-mode-settings",
  swarmModeSettingsBroadcast = "swarm-mode-settings-broadcast",

  // Chat mode
  getChatModeRequest = "get-chat-mode-request",
  getChatModeResponse = "get-chat-mode-response",
}

export type ChatGetAgentEditListRequest =
  WebViewMessage<AgentWebViewMessageType.getEditListRequest> & {
    data: {
      fromTimestamp?: number;
      toTimestamp?: number;
    };
  };

export type ChatGetAgentEditListResponse =
  WebViewMessage<AgentWebViewMessageType.getEditListResponse> & {
    data: ChatGetAgentEditListData;
  };

export type ChatGetAgentEditListData = {
  edits: ChatAgentEdit[];
};

export interface ChatAgentEdit {
  qualifiedPathName: IQualifiedPathName;
  changesSummary?: ChatAgentFileChangeSummary;
}

export interface ChatAgentFileChangeSummary {
  totalAddedLines: number;
  totalRemovedLines: number;

  /** Diff statistics for working directory → staging (unstaged changes) */
  unstagedChanges?: {
    addedLines: number;
    removedLines: number;
  };

  /** Diff statistics for staging → HEAD (staged changes) */
  stagedChanges?: {
    addedLines: number;
    removedLines: number;
  };
}

export type AgentSetCurrentConversation =
  WebViewMessage<AgentWebViewMessageType.setCurrentConversation> & {
    data: { conversationId: string };
  };

export type AgentMigrateConversationId =
  WebViewMessage<AgentWebViewMessageType.migrateConversationId> & {
    data: { oldConversationId: string; newConversationId: string };
  };

export type GetAgentEditChangesByRequestIdRequest =
  WebViewMessage<AgentWebViewMessageType.getEditChangesByRequestIdRequest> & {
    data: RequestIdData;
  };

export type GetAgentEditChangesByRequestIdResponse =
  WebViewMessage<AgentWebViewMessageType.getEditChangesByRequestIdResponse> & {
    data: ChatAgentFileChangeSummary | undefined;
  };

export interface RequestIdData {
  requestId: string;
}

export type RevertToTimestamp =
  WebViewMessage<AgentWebViewMessageType.revertToTimestamp> & {
    data: RevertToTimestampRequestData;
  };

export interface RevertToTimestampRequestData {
  timestamp: number;
  // If specified, only revert these files
  qualifiedPathNames?: IQualifiedPathName[];
}

export type ReportAgentSessionEvent =
  WebViewMessage<AgentWebViewMessageType.reportAgentSessionEvent> & {
    data: AgentSessionEventData;
  };

export type ReportAgentRequestEvent =
  WebViewMessage<AgentWebViewMessageType.reportAgentRequestEvent> & {
    data: AgentRequestEventData;
  };

export type ChatReviewAgentFileMessage =
  WebViewMessage<AgentWebViewMessageType.chatReviewAgentFile> & {
    data: ChatReviewAgentFileData;
  };

export interface ChatReviewAgentFileData {
  qualifiedPathName: IQualifiedPathName;
  fromTimestamp?: number;
  toTimestamp?: number;
  retainFocus?: boolean; // If true, don't open the diff view
  useNativeDiffIfAvailable?: boolean; // If true, use VSCode's native diff viewer when available
}

export type CheckHasEverUsedAgentResponse =
  WebViewMessage<AgentWebViewMessageType.checkHasEverUsedAgentResponse> & {
    data: boolean;
  };

export type SetHasEverUsedAgent =
  WebViewMessage<AgentWebViewMessageType.setHasEverUsedAgent> & {
    data: boolean;
  };

export type CheckHasEverUsedRemoteAgentResponse =
  WebViewMessage<AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse> & {
    data: boolean;
  };

export type SetHasEverUsedRemoteAgent =
  WebViewMessage<AgentWebViewMessageType.setHasEverUsedRemoteAgent> & {
    data: boolean;
  };

export type GetAgentEditContentsByRequestIdRequest =
  WebViewMessage<AgentWebViewMessageType.getAgentEditContentsByRequestId> & {
    data: RequestIdData;
  };

export interface RequestIdData {
  requestId: string;
}

export type GetAgentEditContentsByRequestIdResponse =
  WebViewMessage<AgentWebViewMessageType.getAgentEditContentsByRequestIdResponse> & {
    data: EditContentsData | undefined;
  };

export interface EditContentsData {
  originalCode: string | undefined;
  modifiedCode: string | undefined;
}

export type GetSoundSettingsRequest =
  WebViewMessage<AgentWebViewMessageType.getSoundSettings>;

export type GetSoundSettingsResponse =
  WebViewMessage<AgentWebViewMessageType.getSoundSettingsResponse> & {
    data?: SoundSettings;
  };

export type UpdateSoundSettingsRequest =
  WebViewMessage<AgentWebViewMessageType.updateSoundSettings> & {
    data: Partial<SoundSettings>;
  };

export type SoundSettingsBroadcast =
  WebViewMessage<AgentWebViewMessageType.soundSettingsBroadcast> & {
    data: SoundSettings;
  };

export type GetSwarmModeSettingsRequest =
  WebViewMessage<AgentWebViewMessageType.getSwarmModeSettings>;

export type GetSwarmModeSettingsResponse =
  WebViewMessage<AgentWebViewMessageType.getSwarmModeSettingsResponse> & {
    data?: SwarmModeSettings;
  };

export type UpdateSwarmModeSettingsRequest =
  WebViewMessage<AgentWebViewMessageType.updateSwarmModeSettings> & {
    data: Partial<SwarmModeSettings>;
  };

export type SwarmModeSettingsBroadcast =
  WebViewMessage<AgentWebViewMessageType.swarmModeSettingsBroadcast> & {
    data: SwarmModeSettings;
  };
export type GetChatModeRequest =
  WebViewMessage<AgentWebViewMessageType.getChatModeRequest>;

export type GetChatModeResponse =
  WebViewMessage<AgentWebViewMessageType.getChatModeResponse> & {
    data: { chatMode: ChatMode };
  };
